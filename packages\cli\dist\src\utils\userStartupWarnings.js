/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import fs from 'fs/promises';
import * as os from 'os';
// Individual warning checks
const homeDirectoryCheck = {
    id: 'home-directory',
    check: async (workspaceRoot) => {
        try {
            const [workspaceRealPath, homeRealPath] = await Promise.all([
                fs.realpath(workspaceRoot),
                fs.realpath(os.homedir()),
            ]);
            if (workspaceRealPath === homeRealPath) {
                return 'You are running Gemini CLI in your home directory. It is recommended to run in a project-specific directory.';
            }
            return null;
        }
        catch (_err) {
            return 'Could not verify the current directory due to a file system error.';
        }
    },
};
// All warning checks
const WARNING_CHECKS = [homeDirectoryCheck];
export async function getUserStartupWarnings(workspaceRoot) {
    const results = await Promise.all(WARNING_CHECKS.map((check) => check.check(workspaceRoot)));
    return results.filter((msg) => msg !== null);
}
//# sourceMappingURL=userStartupWarnings.js.map