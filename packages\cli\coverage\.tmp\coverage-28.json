{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/useConsoleMessages.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 639, "endOffset": 5731, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 713, "endOffset": 895, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 781, "endOffset": 833, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 931, "endOffset": 1404, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 999, "endOffset": 1051, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1182, "endOffset": 1243, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1280, "endOffset": 1318, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1465, "endOffset": 1986, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1533, "endOffset": 1585, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1716, "endOffset": 1825, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1862, "endOffset": 1900, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2039, "endOffset": 2710, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2107, "endOffset": 2159, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2392, "endOffset": 2503, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2540, "endOffset": 2578, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2774, "endOffset": 3441, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2842, "endOffset": 2894, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3123, "endOffset": 3234, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3271, "endOffset": 3309, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3482, "endOffset": 4086, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3550, "endOffset": 3602, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3733, "endOffset": 3794, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3831, "endOffset": 3869, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3966, "endOffset": 4024, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4150, "endOffset": 4694, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4218, "endOffset": 4270, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4401, "endOffset": 4462, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4499, "endOffset": 4557, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4594, "endOffset": 4632, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4756, "endOffset": 5300, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4824, "endOffset": 4876, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5007, "endOffset": 5068, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5105, "endOffset": 5163, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5200, "endOffset": 5238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5345, "endOffset": 5727, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5422, "endOffset": 5474, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5605, "endOffset": 5666, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1319", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/useConsoleMessages.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8466, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8466, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 17}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "useConsoleMessages", "ranges": [{"startOffset": 584, "endOffset": 2778, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 917, "endOffset": 1685, "count": 5}, {"startOffset": 971, "endOffset": 992, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1105, "endOffset": 1636, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1201, "endOffset": 1602, "count": 8}, {"startOffset": 1257, "endOffset": 1323, "count": 3}, {"startOffset": 1324, "endOffset": 1396, "count": 1}, {"startOffset": 1398, "endOffset": 1517, "count": 1}, {"startOffset": 1497, "endOffset": 1501, "count": 0}, {"startOffset": 1517, "endOffset": 1594, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1764, "endOffset": 1931, "count": 11}, {"startOffset": 1821, "endOffset": 1927, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2027, "endOffset": 2125, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2231, "endOffset": 2459, "count": 3}, {"startOffset": 2316, "endOffset": 2420, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2509, "endOffset": 2693, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2551, "endOffset": 2687, "count": 9}, {"startOffset": 2612, "endOffset": 2679, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}]}