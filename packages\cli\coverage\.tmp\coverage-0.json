{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/components/shared/text-buffer.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 167024, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 167024, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1041, "endOffset": 6726, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1137, "endOffset": 1347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1406, "endOffset": 2431, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1497, "endOffset": 1970, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2067, "endOffset": 2425, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2488, "endOffset": 3345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2558, "endOffset": 2846, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2909, "endOffset": 3339, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3405, "endOffset": 4468, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3475, "endOffset": 3881, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3968, "endOffset": 4462, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4529, "endOffset": 5749, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4603, "endOffset": 5743, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5820, "endOffset": 6722, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5912, "endOffset": 6716, "count": 1}], "isBlockCoverage": true}, {"functionName": "getBufferState", "ranges": [{"startOffset": 6752, "endOffset": 7157, "count": 93}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7211, "endOffset": 52502, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7274, "endOffset": 7326, "count": 54}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7384, "endOffset": 12727, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7493, "endOffset": 8218, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7572, "endOffset": 7657, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 7643, "endOffset": 7654, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8301, "endOffset": 9035, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8380, "endOffset": 8525, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 8503, "endOffset": 8514, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9133, "endOffset": 10049, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9212, "endOffset": 9439, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 9417, "endOffset": 9428, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10113, "endOffset": 10687, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10192, "endOffset": 10459, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 10437, "endOffset": 10448, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10772, "endOffset": 11299, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10851, "endOffset": 11066, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 11044, "endOffset": 11055, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11383, "endOffset": 11842, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11462, "endOffset": 11681, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 11659, "endOffset": 11670, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11960, "endOffset": 12721, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12039, "endOffset": 12296, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 12274, "endOffset": 12285, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12784, "endOffset": 17602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12880, "endOffset": 13698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12959, "endOffset": 13044, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 13030, "endOffset": 13041, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13090, "endOffset": 13122, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13411, "endOffset": 13443, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13788, "endOffset": 14356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13867, "endOffset": 14010, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 13988, "endOffset": 13999, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14056, "endOffset": 14090, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14129, "endOffset": 14165, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14445, "endOffset": 15318, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14524, "endOffset": 14666, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 14644, "endOffset": 14655, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14712, "endOffset": 14744, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14783, "endOffset": 14813, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15414, "endOffset": 16556, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15493, "endOffset": 15637, "count": 6}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 15615, "endOffset": 15626, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15683, "endOffset": 15735, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15774, "endOffset": 15825, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15864, "endOffset": 15896, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16111, "endOffset": 16143, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16647, "endOffset": 17596, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16726, "endOffset": 16870, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 16848, "endOffset": 16859, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16916, "endOffset": 16942, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17157, "endOffset": 17183, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17670, "endOffset": 19592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17762, "endOffset": 18158, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17841, "endOffset": 17925, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 17912, "endOffset": 17922, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18023, "endOffset": 18060, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18252, "endOffset": 18645, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18331, "endOffset": 18416, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 18402, "endOffset": 18413, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18516, "endOffset": 18553, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18711, "endOffset": 19123, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18790, "endOffset": 18874, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 18861, "endOffset": 18871, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18974, "endOffset": 19011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19216, "endOffset": 19586, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19295, "endOffset": 19379, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 19366, "endOffset": 19376, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19455, "endOffset": 19493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19655, "endOffset": 21899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19769, "endOffset": 20220, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19848, "endOffset": 19993, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 19939, "endOffset": 19949, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20091, "endOffset": 20128, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20325, "endOffset": 20796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20404, "endOffset": 20549, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 20495, "endOffset": 20505, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20655, "endOffset": 20698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20906, "endOffset": 21355, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20985, "endOffset": 21131, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 21076, "endOffset": 21087, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21226, "endOffset": 21263, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21462, "endOffset": 21893, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21541, "endOffset": 21686, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 21632, "endOffset": 21642, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21762, "endOffset": 21800, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21958, "endOffset": 26266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22080, "endOffset": 23393, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22159, "endOffset": 22395, "count": 8}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 22373, "endOffset": 22384, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22441, "endOffset": 22475, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22607, "endOffset": 22641, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22680, "endOffset": 22714, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22753, "endOffset": 22787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22919, "endOffset": 22953, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23172, "endOffset": 23205, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23486, "endOffset": 25132, "count": 1}, {"startOffset": 23986, "endOffset": 25131, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23604, "endOffset": 23746, "count": 11}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 23724, "endOffset": 23735, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23897, "endOffset": 23949, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24026, "endOffset": 24083, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24311, "endOffset": 24363, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24658, "endOffset": 24691, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24982, "endOffset": 25015, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25221, "endOffset": 26260, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25351, "endOffset": 25512, "count": 6}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 25490, "endOffset": 25501, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 25724, "endOffset": 25757, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25796, "endOffset": 25830, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25962, "endOffset": 25995, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26127, "endOffset": 26159, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26334, "endOffset": 29388, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26428, "endOffset": 27190, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26507, "endOffset": 26728, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 26706, "endOffset": 26717, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27299, "endOffset": 29382, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 27378, "endOffset": 27598, "count": 10}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 27576, "endOffset": 27587, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28043, "endOffset": 28076, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28115, "endOffset": 28148, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28275, "endOffset": 28308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28733, "endOffset": 28764, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28803, "endOffset": 28834, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28961, "endOffset": 28992, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29441, "endOffset": 31338, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29526, "endOffset": 30318, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29605, "endOffset": 29690, "count": 5}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 29676, "endOffset": 29687, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 29736, "endOffset": 29768, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29886, "endOffset": 29913, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30117, "endOffset": 30144, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30398, "endOffset": 31332, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30477, "endOffset": 30621, "count": 6}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 30599, "endOffset": 30610, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 30667, "endOffset": 30699, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30738, "endOffset": 30768, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30891, "endOffset": 30918, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 31126, "endOffset": 31153, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 31398, "endOffset": 34067, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 31504, "endOffset": 32008, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 31583, "endOffset": 31668, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 31654, "endOffset": 31665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 31714, "endOffset": 31747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 32112, "endOffset": 32904, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 32191, "endOffset": 32333, "count": 5}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 32311, "endOffset": 32322, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 32379, "endOffset": 32411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 32450, "endOffset": 32482, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 32695, "endOffset": 32727, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 33024, "endOffset": 34061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 33103, "endOffset": 33272, "count": 5}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 33250, "endOffset": 33261, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 33318, "endOffset": 33352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 33579, "endOffset": 33613, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 33836, "endOffset": 33869, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 34122, "endOffset": 43457, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 34201, "endOffset": 34938, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 34280, "endOffset": 34365, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 34351, "endOffset": 34362, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 34420, "endOffset": 34604, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 34659, "endOffset": 34843, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35014, "endOffset": 35526, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35093, "endOffset": 35178, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 35164, "endOffset": 35175, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 35233, "endOffset": 35423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35595, "endOffset": 36226, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35674, "endOffset": 35815, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 35793, "endOffset": 35804, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 35861, "endOffset": 35893, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35941, "endOffset": 36133, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36319, "endOffset": 37453, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36398, "endOffset": 36543, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 36521, "endOffset": 36532, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 36589, "endOffset": 36621, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36747, "endOffset": 37278, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 37546, "endOffset": 38202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 37625, "endOffset": 37770, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 37748, "endOffset": 37759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 37816, "endOffset": 37848, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 37974, "endOffset": 38027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 38308, "endOffset": 38972, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 38387, "endOffset": 38532, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 38510, "endOffset": 38521, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 38578, "endOffset": 38610, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 38736, "endOffset": 38793, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 39049, "endOffset": 40025, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 39128, "endOffset": 39270, "count": 5}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 39248, "endOffset": 39259, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 39316, "endOffset": 39348, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 39396, "endOffset": 39588, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 39730, "endOffset": 39923, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40113, "endOffset": 40700, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40192, "endOffset": 40277, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 40263, "endOffset": 40274, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40404, "endOffset": 40596, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40792, "endOffset": 41303, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40871, "endOffset": 40956, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 40942, "endOffset": 40953, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 41011, "endOffset": 41200, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 41396, "endOffset": 43451, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 42787, "endOffset": 42872, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 42858, "endOffset": 42869, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 42918, "endOffset": 43056, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 43513, "endOffset": 49171, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 43614, "endOffset": 44132, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 43693, "endOffset": 43837, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 43815, "endOffset": 43826, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 43883, "endOffset": 43940, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 44227, "endOffset": 44764, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 44306, "endOffset": 44465, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 44443, "endOffset": 44454, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 44511, "endOffset": 44565, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 44861, "endOffset": 45375, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 44940, "endOffset": 45091, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 45069, "endOffset": 45080, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 45137, "endOffset": 45187, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 45467, "endOffset": 45986, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 45546, "endOffset": 45691, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 45669, "endOffset": 45680, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 45737, "endOffset": 45792, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 46072, "endOffset": 46592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 46151, "endOffset": 46296, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 46274, "endOffset": 46285, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 46342, "endOffset": 46397, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 46681, "endOffset": 47202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 46760, "endOffset": 46908, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 46886, "endOffset": 46897, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 46954, "endOffset": 47011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 47290, "endOffset": 47818, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 47369, "endOffset": 47524, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 47502, "endOffset": 47513, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 47570, "endOffset": 47621, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 47926, "endOffset": 48550, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 48005, "endOffset": 48149, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 48127, "endOffset": 48138, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 48195, "endOffset": 48267, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 48388, "endOffset": 48460, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 48642, "endOffset": 49165, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 48721, "endOffset": 48881, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 48859, "endOffset": 48870, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 48927, "endOffset": 48977, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 49233, "endOffset": 52498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 49319, "endOffset": 49879, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 49398, "endOffset": 49483, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 49469, "endOffset": 49480, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 49589, "endOffset": 49781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 49961, "endOffset": 50532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 50040, "endOffset": 50125, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 50111, "endOffset": 50122, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 50234, "endOffset": 50434, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 50629, "endOffset": 51190, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 50708, "endOffset": 50793, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 50779, "endOffset": 50790, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 50899, "endOffset": 51092, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 51278, "endOffset": 51842, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 51357, "endOffset": 51442, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 51428, "endOffset": 51439, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 51553, "endOffset": 51742, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 51925, "endOffset": 52492, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 52004, "endOffset": 52089, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 52075, "endOffset": 52086, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 52198, "endOffset": 52388, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 52562, "endOffset": 58086, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 52637, "endOffset": 52763, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 52831, "endOffset": 53305, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 53372, "endOffset": 54516, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 54579, "endOffset": 55162, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 55240, "endOffset": 55606, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 55686, "endOffset": 56052, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 56122, "endOffset": 56347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 56438, "endOffset": 57239, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 57326, "endOffset": 57584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 57719, "endOffset": 58082, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1321", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/components/shared/text-buffer.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 127577, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 127577, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 41}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 406, "count": 11}, {"startOffset": 396, "endOffset": 404, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 450, "endOffset": 497, "count": 203}, {"startOffset": 487, "endOffset": 495, "count": 0}], "isBlockCoverage": true}, {"functionName": "isWordChar", "ranges": [{"startOffset": 1939, "endOffset": 2044, "count": 0}], "isBlockCoverage": false}, {"functionName": "stripUnsafeCharacters", "ranges": [{"startOffset": 2045, "endOffset": 2502, "count": 33}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2242, "endOffset": 2489, "count": 4106}, {"startOffset": 2279, "endOffset": 2292, "count": 0}, {"startOffset": 2356, "endOffset": 2383, "count": 0}, {"startOffset": 2433, "endOffset": 2447, "count": 31}, {"startOffset": 2448, "endOffset": 2462, "count": 31}], "isBlockCoverage": true}, {"functionName": "clamp", "ranges": [{"startOffset": 2503, "endOffset": 2578, "count": 67}, {"startOffset": 2550, "endOffset": 2555, "count": 0}, {"startOffset": 2566, "endOffset": 2571, "count": 1}, {"startOffset": 2572, "endOffset": 2575, "count": 66}], "isBlockCoverage": true}, {"functionName": "calculateInitialCursorPosition", "ranges": [{"startOffset": 2579, "endOffset": 3223, "count": 54}, {"startOffset": 2725, "endOffset": 3042, "count": 55}, {"startOffset": 2885, "endOffset": 2888, "count": 8}, {"startOffset": 2889, "endOffset": 2892, "count": 47}, {"startOffset": 2933, "endOffset": 2976, "count": 54}, {"startOffset": 2976, "endOffset": 3042, "count": 1}, {"startOffset": 3042, "endOffset": 3222, "count": 0}], "isBlockCoverage": true}, {"functionName": "offsetToLogicalPos", "ranges": [{"startOffset": 3224, "endOffset": 4208, "count": 41}, {"startOffset": 3343, "endOffset": 3357, "count": 8}, {"startOffset": 3357, "endOffset": 3433, "count": 33}, {"startOffset": 3433, "endOffset": 4033, "count": 70}, {"startOffset": 3593, "endOffset": 3596, "count": 55}, {"startOffset": 3597, "endOffset": 3600, "count": 15}, {"startOffset": 3649, "endOffset": 3732, "count": 19}, {"startOffset": 3732, "endOffset": 3984, "count": 51}, {"startOffset": 3791, "endOffset": 3984, "count": 9}, {"startOffset": 3952, "endOffset": 3984, "count": 0}, {"startOffset": 3984, "endOffset": 4033, "count": 42}, {"startOffset": 4033, "endOffset": 4147, "count": 5}, {"startOffset": 4147, "endOffset": 4184, "count": 0}, {"startOffset": 4184, "endOffset": 4207, "count": 5}], "isBlockCoverage": true}, {"functionName": "calculateVisualLayout", "ranges": [{"startOffset": 4210, "endOffset": 9542, "count": 183}, {"startOffset": 8801, "endOffset": 8826, "count": 124}, {"startOffset": 8828, "endOffset": 9081, "count": 25}, {"startOffset": 8864, "endOffset": 9042, "count": 0}, {"startOffset": 9081, "endOffset": 9420, "count": 158}, {"startOffset": 9136, "endOffset": 9230, "count": 114}, {"startOffset": 9231, "endOffset": 9256, "count": 53}, {"startOffset": 9258, "endOffset": 9420, "count": 53}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4438, "endOffset": 8737, "count": 321}, {"startOffset": 4532, "endOffset": 4815, "count": 41}, {"startOffset": 4714, "endOffset": 4739, "count": 33}, {"startOffset": 4741, "endOffset": 4809, "count": 33}, {"startOffset": 4815, "endOffset": 8733, "count": 280}, {"startOffset": 5004, "endOffset": 8248, "count": 855}, {"startOffset": 5278, "endOffset": 6482, "count": 7009}, {"startOffset": 5475, "endOffset": 6204, "count": 575}, {"startOffset": 5519, "endOffset": 5554, "count": 492}, {"startOffset": 5555, "endOffset": 5612, "count": 492}, {"startOffset": 5614, "endOffset": 5882, "count": 492}, {"startOffset": 5882, "endOffset": 6172, "count": 83}, {"startOffset": 5935, "endOffset": 5969, "count": 0}, {"startOffset": 5971, "endOffset": 6067, "count": 0}, {"startOffset": 6104, "endOffset": 6139, "count": 0}, {"startOffset": 6141, "endOffset": 6158, "count": 0}, {"startOffset": 6204, "endOffset": 6354, "count": 6434}, {"startOffset": 6354, "endOffset": 6472, "count": 695}, {"startOffset": 6523, "endOffset": 6574, "count": 0}, {"startOffset": 6576, "endOffset": 6729, "count": 0}, {"startOffset": 6770, "endOffset": 6821, "count": 0}, {"startOffset": 6823, "endOffset": 6937, "count": 0}, {"startOffset": 7204, "endOffset": 7836, "count": 229}, {"startOffset": 7305, "endOffset": 7365, "count": 178}, {"startOffset": 7367, "endOffset": 7590, "count": 96}, {"startOffset": 7590, "endOffset": 7826, "count": 133}, {"startOffset": 7660, "endOffset": 7687, "count": 55}, {"startOffset": 7689, "endOffset": 7826, "count": 55}, {"startOffset": 8040, "endOffset": 8091, "count": 575}, {"startOffset": 8092, "endOffset": 8194, "count": 575}, {"startOffset": 8196, "endOffset": 8240, "count": 516}, {"startOffset": 8290, "endOffset": 8340, "count": 150}, {"startOffset": 8342, "endOffset": 8727, "count": 51}], "isBlockCoverage": true}, {"functionName": "textBufferReducer", "ranges": [{"startOffset": 9569, "endOffset": 26845, "count": 166}, {"startOffset": 10168, "endOffset": 10722, "count": 2}, {"startOffset": 10255, "endOffset": 10301, "count": 1}, {"startOffset": 10434, "endOffset": 10440, "count": 0}, {"startOffset": 10673, "endOffset": 10678, "count": 0}, {"startOffset": 10727, "endOffset": 12224, "count": 33}, {"startOffset": 11379, "endOffset": 11870, "count": 9}, {"startOffset": 11534, "endOffset": 11539, "count": 0}, {"startOffset": 11870, "endOffset": 12053, "count": 24}, {"startOffset": 12229, "endOffset": 13414, "count": 16}, {"startOffset": 12509, "endOffset": 12530, "count": 2}, {"startOffset": 12532, "endOffset": 12545, "count": 0}, {"startOffset": 12574, "endOffset": 12828, "count": 14}, {"startOffset": 12828, "endOffset": 13243, "count": 2}, {"startOffset": 13419, "endOffset": 13594, "count": 54}, {"startOffset": 13529, "endOffset": 13594, "count": 0}, {"startOffset": 13599, "endOffset": 18478, "count": 41}, {"startOffset": 14148, "endOffset": 14153, "count": 0}, {"startOffset": 14185, "endOffset": 14485, "count": 4}, {"startOffset": 14264, "endOffset": 14305, "count": 3}, {"startOffset": 14305, "endOffset": 14467, "count": 1}, {"startOffset": 14448, "endOffset": 14453, "count": 0}, {"startOffset": 14494, "endOffset": 14769, "count": 15}, {"startOffset": 14590, "endOffset": 14631, "count": 14}, {"startOffset": 14631, "endOffset": 14751, "count": 1}, {"startOffset": 14778, "endOffset": 15130, "count": 3}, {"startOffset": 14865, "endOffset": 14896, "count": 0}, {"startOffset": 15079, "endOffset": 15084, "count": 0}, {"startOffset": 15139, "endOffset": 15514, "count": 7}, {"startOffset": 15249, "endOffset": 15280, "count": 4}, {"startOffset": 15463, "endOffset": 15468, "count": 0}, {"startOffset": 15523, "endOffset": 15614, "count": 2}, {"startOffset": 15623, "endOffset": 15729, "count": 10}, {"startOffset": 15738, "endOffset": 16999, "count": 0}, {"startOffset": 17008, "endOffset": 17998, "count": 0}, {"startOffset": 18007, "endOffset": 18032, "count": 0}, {"startOffset": 18373, "endOffset": 18378, "count": 0}, {"startOffset": 18451, "endOffset": 18478, "count": 0}, {"startOffset": 18483, "endOffset": 19377, "count": 2}, {"startOffset": 18652, "endOffset": 19350, "count": 1}, {"startOffset": 19350, "endOffset": 19377, "count": 0}, {"startOffset": 19382, "endOffset": 21021, "count": 0}, {"startOffset": 21026, "endOffset": 22151, "count": 0}, {"startOffset": 22156, "endOffset": 22975, "count": 0}, {"startOffset": 22980, "endOffset": 23475, "count": 0}, {"startOffset": 23480, "endOffset": 23935, "count": 3}, {"startOffset": 23596, "endOffset": 23609, "count": 0}, {"startOffset": 23940, "endOffset": 24395, "count": 3}, {"startOffset": 24056, "endOffset": 24069, "count": 0}, {"startOffset": 24400, "endOffset": 26312, "count": 10}, {"startOffset": 24527, "endOffset": 24570, "count": 9}, {"startOffset": 24550, "endOffset": 24570, "count": 7}, {"startOffset": 24571, "endOffset": 24586, "count": 8}, {"startOffset": 24587, "endOffset": 24602, "count": 8}, {"startOffset": 24603, "endOffset": 24634, "count": 8}, {"startOffset": 24635, "endOffset": 24700, "count": 8}, {"startOffset": 24702, "endOffset": 24733, "count": 2}, {"startOffset": 24733, "endOffset": 25301, "count": 8}, {"startOffset": 25301, "endOffset": 25380, "count": 6}, {"startOffset": 25380, "endOffset": 25916, "count": 2}, {"startOffset": 25578, "endOffset": 25908, "count": 0}, {"startOffset": 25916, "endOffset": 26045, "count": 8}, {"startOffset": 26045, "endOffset": 26048, "count": 0}, {"startOffset": 26049, "endOffset": 26055, "count": 8}, {"startOffset": 26317, "endOffset": 26618, "count": 0}, {"startOffset": 26623, "endOffset": 26689, "count": 1}, {"startOffset": 26694, "endOffset": 26839, "count": 1}], "isBlockCoverage": true}, {"functionName": "pushUndo", "ranges": [{"startOffset": 9632, "endOffset": 10005, "count": 61}, {"startOffset": 9901, "endOffset": 9932, "count": 0}], "isBlockCoverage": true}, {"functionName": "currentLine", "ranges": [{"startOffset": 10029, "endOffset": 10056, "count": 45}, {"startOffset": 10051, "endOffset": 10056, "count": 0}], "isBlockCoverage": true}, {"functionName": "currentLineLen", "ranges": [{"startOffset": 10083, "endOffset": 10137, "count": 26}], "isBlockCoverage": true}, {"functionName": "currentLine2", "ranges": [{"startOffset": 10949, "endOffset": 10973, "count": 33}, {"startOffset": 10968, "endOffset": 10973, "count": 0}], "isBlockCoverage": true}, {"functionName": "currentLine2", "ranges": [{"startOffset": 12454, "endOffset": 12478, "count": 18}, {"startOffset": 12473, "endOffset": 12478, "count": 0}], "isBlockCoverage": true}, {"functionName": "useTextBuffer", "ranges": [{"startOffset": 26847, "endOffset": 36325, "count": 203}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 27069, "endOffset": 27577, "count": 54}, {"startOffset": 27228, "endOffset": 27234, "count": 0}, {"startOffset": 27324, "endOffset": 27330, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 27860, "endOffset": 27882, "count": 102}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 27956, "endOffset": 28035, "count": 142}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28276, "endOffset": 28335, "count": 102}, {"startOffset": 28302, "endOffset": 28331, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28394, "endOffset": 28476, "count": 54}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28535, "endOffset": 28942, "count": 146}, {"startOffset": 28665, "endOffset": 28716, "count": 1}, {"startOffset": 28716, "endOffset": 28835, "count": 145}, {"startOffset": 28771, "endOffset": 28835, "count": 3}, {"startOffset": 28885, "endOffset": 28938, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29048, "endOffset": 30163, "count": 26}, {"startOffset": 29087, "endOffset": 29163, "count": 4}, {"startOffset": 29163, "endOffset": 29259, "count": 22}, {"startOffset": 29259, "endOffset": 29278, "count": 14}, {"startOffset": 29280, "endOffset": 29653, "count": 11}, {"startOffset": 29384, "endOffset": 29414, "count": 1}, {"startOffset": 29416, "endOffset": 29470, "count": 1}, {"startOffset": 29598, "endOffset": 29645, "count": 2}, {"startOffset": 29653, "endOffset": 29752, "count": 22}, {"startOffset": 29752, "endOffset": 30052, "count": 263}, {"startOffset": 29795, "endOffset": 29996, "count": 6}, {"startOffset": 29835, "endOffset": 29942, "count": 1}, {"startOffset": 29996, "endOffset": 30044, "count": 257}, {"startOffset": 30052, "endOffset": 30088, "count": 22}, {"startOffset": 30088, "endOffset": 30157, "count": 21}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30261, "endOffset": 30321, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30386, "endOffset": 30434, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30493, "endOffset": 30538, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30598, "endOffset": 30662, "count": 41}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30722, "endOffset": 30765, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30825, "endOffset": 30868, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30931, "endOffset": 31003, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 31073, "endOffset": 31128, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 31199, "endOffset": 31255, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 31324, "endOffset": 31378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 31446, "endOffset": 31499, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 31580, "endOffset": 33035, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 33137, "endOffset": 34716, "count": 16}, {"startOffset": 33219, "endOffset": 33236, "count": 14}, {"startOffset": 33237, "endOffset": 33254, "count": 14}, {"startOffset": 33255, "endOffset": 33274, "count": 14}, {"startOffset": 33284, "endOffset": 33294, "count": 2}, {"startOffset": 33294, "endOffset": 34710, "count": 14}, {"startOffset": 33330, "endOffset": 33342, "count": 1}, {"startOffset": 33343, "endOffset": 33355, "count": 1}, {"startOffset": 33357, "endOffset": 33370, "count": 1}, {"startOffset": 33370, "endOffset": 34710, "count": 13}, {"startOffset": 33395, "endOffset": 33414, "count": 0}, {"startOffset": 33416, "endOffset": 33429, "count": 0}, {"startOffset": 33466, "endOffset": 33478, "count": 1}, {"startOffset": 33479, "endOffset": 33491, "count": 1}, {"startOffset": 33493, "endOffset": 33507, "count": 1}, {"startOffset": 33507, "endOffset": 34710, "count": 12}, {"startOffset": 33532, "endOffset": 33551, "count": 0}, {"startOffset": 33553, "endOffset": 33567, "count": 0}, {"startOffset": 33602, "endOffset": 33613, "count": 0}, {"startOffset": 33650, "endOffset": 33663, "count": 0}, {"startOffset": 33702, "endOffset": 33724, "count": 0}, {"startOffset": 33726, "endOffset": 33743, "count": 0}, {"startOffset": 33768, "endOffset": 33787, "count": 0}, {"startOffset": 33789, "endOffset": 33806, "count": 0}, {"startOffset": 33845, "endOffset": 33868, "count": 0}, {"startOffset": 33878, "endOffset": 33896, "count": 0}, {"startOffset": 33921, "endOffset": 33940, "count": 0}, {"startOffset": 33942, "endOffset": 33960, "count": 0}, {"startOffset": 33997, "endOffset": 34010, "count": 0}, {"startOffset": 34035, "endOffset": 34054, "count": 0}, {"startOffset": 34056, "endOffset": 34069, "count": 0}, {"startOffset": 34105, "endOffset": 34117, "count": 0}, {"startOffset": 34142, "endOffset": 34161, "count": 0}, {"startOffset": 34163, "endOffset": 34175, "count": 0}, {"startOffset": 34200, "endOffset": 34219, "count": 0}, {"startOffset": 34221, "endOffset": 34238, "count": 0}, {"startOffset": 34277, "endOffset": 34323, "count": 0}, {"startOffset": 34333, "endOffset": 34350, "count": 0}, {"startOffset": 34389, "endOffset": 34413, "count": 0}, {"startOffset": 34423, "endOffset": 34441, "count": 0}, {"startOffset": 34482, "endOffset": 34498, "count": 8}, {"startOffset": 34499, "endOffset": 34530, "count": 8}, {"startOffset": 34511, "endOffset": 34530, "count": 0}, {"startOffset": 34540, "endOffset": 34552, "count": 4}, {"startOffset": 34552, "endOffset": 34710, "count": 8}, {"startOffset": 34602, "endOffset": 34621, "count": 0}, {"startOffset": 34623, "endOffset": 34629, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 34869, "endOffset": 34944, "count": 146}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35069, "endOffset": 35250, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35338, "endOffset": 35605, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 35698, "endOffset": 35778, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1330", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1814, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1814, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 390, "endOffset": 472, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 533, "endOffset": 619, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1331", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11673, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11673, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 312, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1332", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/config/config.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 50707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 50707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 390, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 467, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 524, "endOffset": 607, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4742, "endOffset": 4889, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4951, "endOffset": 5832, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5847, "endOffset": 17225, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1336", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/contentGenerator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 517, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1341, "endOffset": 1555, "count": 1}], "isBlockCoverage": true}, {"functionName": "createContentGeneratorConfig", "ranges": [{"startOffset": 1587, "endOffset": 3116, "count": 0}], "isBlockCoverage": false}, {"functionName": "createContentGenerator", "ranges": [{"startOffset": 3118, "endOffset": 4086, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1543", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/codeAssist.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3709, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3709, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 269, "endOffset": 335, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCodeAssistContentGenerator", "ranges": [{"startOffset": 1115, "endOffset": 1677, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1544", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/oauth2.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 396, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 451, "endOffset": 509, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 565, "endOffset": 624, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 676, "endOffset": 731, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOauthClient", "ranges": [{"startOffset": 3741, "endOffset": 6279, "count": 0}], "isBlockCoverage": false}, {"functionName": "authWithWeb", "ranges": [{"startOffset": 6281, "endOffset": 9224, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailablePort", "ranges": [{"startOffset": 9225, "endOffset": 9840, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCachedCredentials", "ranges": [{"startOffset": 9842, "endOffset": 10480, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheCredentials", "ranges": [{"startOffset": 10481, "endOffset": 10774, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedCredentialPath", "ranges": [{"startOffset": 10775, "endOffset": 10926, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGoogleAccountIdCachePath", "ranges": [{"startOffset": 10927, "endOffset": 11089, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheGoogleAccountId", "ranges": [{"startOffset": 11090, "endOffset": 11348, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedGoogleAccountId", "ranges": [{"startOffset": 11349, "endOffset": 11715, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearCachedCredentialFile", "ranges": [{"startOffset": 11717, "endOffset": 12029, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRawGoogleAccountId", "ranges": [{"startOffset": 12239, "endOffset": 13379, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1557", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/errors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6685, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6685, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 0}], "isBlockCoverage": false}, {"functionName": "isNodeError", "ranges": [{"startOffset": 1030, "endOffset": 1115, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1117, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1459, "endOffset": 2277, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 2279, "endOffset": 2537, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1565", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8065, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8065, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "ProjectIdRequiredError", "ranges": [{"startOffset": 883, "endOffset": 1041, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupUser", "ranges": [{"startOffset": 1147, "endOffset": 2390, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOnboardTier", "ranges": [{"startOffset": 2392, "endOffset": 2777, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1566", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 404, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 456, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 837, "endOffset": 1482, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2147, "endOffset": 2377, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1567", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/server.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23446, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23446, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 422, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1303, "endOffset": 7234, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1572", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/converter.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 650, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCountTokenRequest", "ranges": [{"startOffset": 926, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromCountTokenResponse", "ranges": [{"startOffset": 1105, "endOffset": 1203, "count": 0}], "isBlockCoverage": false}, {"functionName": "toGenerateContentRequest", "ranges": [{"startOffset": 1205, "endOffset": 1395, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromGenerateContentResponse", "ranges": [{"startOffset": 1397, "endOffset": 1771, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerateContentRequest", "ranges": [{"startOffset": 1773, "endOffset": 2281, "count": 0}], "isBlockCoverage": false}, {"functionName": "toContents", "ranges": [{"startOffset": 2282, "endOffset": 2515, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2516, "endOffset": 2636, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2637, "endOffset": 3170, "count": 0}], "isBlockCoverage": false}, {"functionName": "toParts", "ranges": [{"startOffset": 3171, "endOffset": 3228, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON>", "ranges": [{"startOffset": 3229, "endOffset": 3370, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerationConfig", "ranges": [{"startOffset": 3371, "endOffset": 4380, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1573", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/config/models.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1684, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1684, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 368, "endOffset": 428, "count": 1}, {"startOffset": 418, "endOffset": 426, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 489, "endOffset": 553, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1574", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/modelCheck.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7710, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7710, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEffectiveModel", "ranges": [{"startOffset": 1018, "endOffset": 2814, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1575", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/tool-registry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41121, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41121, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 437, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1900, "endOffset": 5313, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5334, "endOffset": 12112, "count": 0}], "isBlockCoverage": true}, {"functionName": "sanitizeParameters", "ranges": [{"startOffset": 12713, "endOffset": 12796, "count": 0}], "isBlockCoverage": false}, {"functionName": "_sanitizeParameters", "ranges": [{"startOffset": 12994, "endOffset": 14077, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1576", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/tools.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15463, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15463, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 13}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 571, "endOffset": 3280, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3312, "endOffset": 3750, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1577", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/mcp-client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 462, "endOffset": 513, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 570, "endOffset": 630, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 690, "endOffset": 753, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 802, "endOffset": 854, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 908, "endOffset": 965, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1070, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1117, "endOffset": 1167, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2902, "endOffset": 3239, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3370, "endOffset": 3707, "count": 1}], "isBlockCoverage": true}, {"functionName": "addMCPStatusChangeListener", "ranges": [{"startOffset": 4074, "endOffset": 4165, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeMCPStatusChangeListener", "ranges": [{"startOffset": 4226, "endOffset": 4415, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateMCPServerStatus", "ranges": [{"startOffset": 4463, "endOffset": 4696, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPServerStatus", "ranges": [{"startOffset": 4748, "endOffset": 4879, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllMCPServerStatuses", "ranges": [{"startOffset": 4920, "endOffset": 5005, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPDiscoveryState", "ranges": [{"startOffset": 5054, "endOffset": 5119, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverMcpTools", "ranges": [{"startOffset": 5121, "endOffset": 6262, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectAndDiscover", "ranges": [{"startOffset": 6794, "endOffset": 15128, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1669", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/mcp-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16421, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16421, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 673, "endOffset": 3111, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 673, "endOffset": 3111, "count": 0}], "isBlockCoverage": true}, {"functionName": "getStringifiedResultForDisplay", "ranges": [{"startOffset": 4240, "endOffset": 5653, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1670", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/ls.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29509, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 29509, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1434, "endOffset": 10361, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1434, "endOffset": 10361, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1671", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/schemaValidator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8007, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8007, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "validate", "ranges": [{"startOffset": 854, "endOffset": 1336, "count": 0}], "isBlockCoverage": false}, {"functionName": "toObjectSchema", "ranges": [{"startOffset": 1599, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1755", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/paths.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 334, "endOffset": 380, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 422, "endOffset": 467, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 510, "endOffset": 556, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 597, "endOffset": 641, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 684, "endOffset": 730, "count": 11}, {"startOffset": 720, "endOffset": 728, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 775, "endOffset": 823, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 871, "endOffset": 922, "count": 0}], "isBlockCoverage": false}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1548, "endOffset": 1743, "count": 0}], "isBlockCoverage": false}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1911, "endOffset": 4322, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 4760, "endOffset": 5201, "count": 0}], "isBlockCoverage": false}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 5245, "endOffset": 5608, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 5654, "endOffset": 5731, "count": 11}], "isBlockCoverage": true}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 5931, "endOffset": 6060, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 6264, "endOffset": 6473, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1756", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/read-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16973, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16973, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1752, "endOffset": 6265, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1752, "endOffset": 6265, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1757", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/fileUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 36964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 495, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 538, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 629, "endOffset": 677, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 790, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSpecificMimeType", "ranges": [{"startOffset": 1719, "endOffset": 1906, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 2149, "endOffset": 2941, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinaryFile", "ranges": [{"startOffset": 3107, "endOffset": 4503, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectFileType", "ranges": [{"startOffset": 4677, "endOffset": 6461, "count": 0}], "isBlockCoverage": false}, {"functionName": "processSingleFileContent", "ranges": [{"startOffset": 6861, "endOffset": 13076, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1764", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/metrics.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18804, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18804, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 426, "endOffset": 477, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 750, "endOffset": 808, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 860, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 971, "endOffset": 1030, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1681, "endOffset": 1825, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2070, "endOffset": 2175, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMeter", "ranges": [{"startOffset": 2176, "endOffset": 2347, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeMetrics", "ranges": [{"startOffset": 2349, "endOffset": 4246, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordToolCallMetrics", "ranges": [{"startOffset": 4248, "endOffset": 4762, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordTokenUsageMetrics", "ranges": [{"startOffset": 4764, "endOffset": 5019, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiResponseMetrics", "ranges": [{"startOffset": 5021, "endOffset": 5537, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiErrorMetrics", "ranges": [{"startOffset": 5539, "endOffset": 6083, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordFileOperationMetric", "ranges": [{"startOffset": 6085, "endOffset": 6588, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1860", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/constants.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 440, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 537, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 634, "endOffset": 683, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 784, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 881, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 934, "endOffset": 990, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1045, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1273, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1382, "endOffset": 1434, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1597, "endOffset": 1658, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1861", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/grep.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 63513, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 63513, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2499, "endOffset": 22317, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2499, "endOffset": 22317, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1881", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/gitUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6303, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6303, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 844, "endOffset": 1653, "count": 0}], "isBlockCoverage": false}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1842, "endOffset": 2439, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1882", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/glob.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 31867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 31867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 382, "count": 0}], "isBlockCoverage": false}, {"functionName": "sortFileEntries", "ranges": [{"startOffset": 1799, "endOffset": 2492, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2624, "endOffset": 11131, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2624, "endOffset": 11131, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1883", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/edit.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 55752, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 55752, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2389, "endOffset": 19604, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2389, "endOffset": 19604, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1885", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/editCorrector.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 81034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 81034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 578, "endOffset": 628, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 683, "endOffset": 741, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 793, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 905, "endOffset": 965, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1012, "endOffset": 1062, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1127, "endOffset": 1195, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTimestampFromFunctionId", "ranges": [{"startOffset": 3627, "endOffset": 3888, "count": 0}], "isBlockCoverage": false}, {"functionName": "findLastEditTimestamp", "ranges": [{"startOffset": 4254, "endOffset": 6803, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectEdit", "ranges": [{"startOffset": 7332, "endOffset": 13596, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectFileContent", "ranges": [{"startOffset": 13598, "endOffset": 14191, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctOldStringMismatch", "ranges": [{"startOffset": 14681, "endOffset": 17041, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewString", "ranges": [{"startOffset": 17647, "endOffset": 20453, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewStringEscaping", "ranges": [{"startOffset": 20936, "endOffset": 23240, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctStringEscaping", "ranges": [{"startOffset": 23672, "endOffset": 25581, "count": 0}], "isBlockCoverage": false}, {"functionName": "trimPairIfPossible", "ranges": [{"startOffset": 25583, "endOffset": 26243, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapeStringForGeminiBug", "ranges": [{"startOffset": 26321, "endOffset": 28630, "count": 0}], "isBlockCoverage": false}, {"functionName": "countOccurrences", "ranges": [{"startOffset": 28689, "endOffset": 28998, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetEditCorrectorCaches_TEST_ONLY", "ranges": [{"startOffset": 29000, "endOffset": 29122, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1886", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/write-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 42741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 42741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2724, "endOffset": 15185, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2724, "endOffset": 15185, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1887", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/diffOptions.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1271, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1271, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1888", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/read-many-files.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 52914, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 52914, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 3380, "endOffset": 18642, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3380, "endOffset": 18642, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1889", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/memoryTool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26084, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26084, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 470, "endOffset": 525, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 575, "endOffset": 628, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 685, "endOffset": 745, "count": 1}, {"startOffset": 735, "endOffset": 743, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 799, "endOffset": 856, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 897, "endOffset": 941, "count": 0}], "isBlockCoverage": false}, {"functionName": "setGeminiMdFilename", "ranges": [{"startOffset": 3915, "endOffset": 4247, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentGeminiMdFilename", "ranges": [{"startOffset": 4249, "endOffset": 4426, "count": 1}, {"startOffset": 4337, "endOffset": 4387, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAllGeminiMdFilenames", "ranges": [{"startOffset": 4428, "endOffset": 4601, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGlobalMemoryFilePath", "ranges": [{"startOffset": 4603, "endOffset": 4740, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureNewlineSeparation", "ranges": [{"startOffset": 4812, "endOffset": 5133, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 5217, "endOffset": 9360, "count": 1}], "isBlockCoverage": true}, {"functionName": "MemoryTool", "ranges": [{"startOffset": 5268, "endOffset": 5392, "count": 0}], "isBlockCoverage": false}, {"functionName": "performAddMemoryEntry", "ranges": [{"startOffset": 5404, "endOffset": 7822, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7827, "endOffset": 9358, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1890", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/LruCache.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3889, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3889, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 2}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 393, "endOffset": 1181, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 423, "endOffset": 515, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 520, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 766, "endOffset": 1131, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1136, "endOffset": 1179, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1891", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/messageInspectors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2484, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2484, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 352, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFunctionResponse", "ranges": [{"startOffset": 491, "endOffset": 662, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFunctionCall", "ranges": [{"startOffset": 664, "endOffset": 828, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1892", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/shell.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 58783, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 58783, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2048, "endOffset": 20771, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2048, "endOffset": 20771, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1893", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/web-fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37380, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37380, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractUrls", "ranges": [{"startOffset": 1920, "endOffset": 2034, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2173, "endOffset": 12420, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2173, "endOffset": 12420, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1894", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/generateContentResponseUtilities.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11697, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11697, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 567, "endOffset": 626, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 679, "endOffset": 735, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 797, "endOffset": 862, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 914, "endOffset": 969, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1030, "endOffset": 1094, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseText", "ranges": [{"startOffset": 1185, "endOffset": 1556, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseTextFromParts", "ranges": [{"startOffset": 1558, "endOffset": 1875, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCalls", "ranges": [{"startOffset": 1877, "endOffset": 2226, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromParts", "ranges": [{"startOffset": 2228, "endOffset": 2523, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsAsJson", "ranges": [{"startOffset": 2525, "endOffset": 2734, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromPartsAsJson", "ranges": [{"startOffset": 2736, "endOffset": 2957, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponse", "ranges": [{"startOffset": 2959, "endOffset": 3374, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponseFromParts", "ranges": [{"startOffset": 3376, "endOffset": 3809, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1895", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5599, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5599, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 333, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 425, "endOffset": 475, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1111, "endOffset": 1247, "count": 0}], "isBlockCoverage": true}, {"functionName": "isPrivateIp", "ranges": [{"startOffset": 1249, "endOffset": 1459, "count": 0}], "isBlockCoverage": false}, {"functionName": "fetchWithTimeout", "ranges": [{"startOffset": 1461, "endOffset": 2087, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1928", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/web-search.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1425, "endOffset": 6487, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1425, "endOffset": 6487, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1929", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 62593, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 62593, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 358, "endOffset": 404, "count": 0}], "isBlockCoverage": false}, {"functionName": "isThinkingSupported", "ranges": [{"startOffset": 2827, "endOffset": 2945, "count": 0}], "isBlockCoverage": false}, {"functionName": "findIndexAfterFraction", "ranges": [{"startOffset": 3086, "endOffset": 3730, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3751, "endOffset": 21261, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1930", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/getFolderStructure.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38265, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38265, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "readFullStructure", "ranges": [{"startOffset": 962, "endOffset": 6781, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatStructure", "ranges": [{"startOffset": 7049, "endOffset": 9323, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFolderStructure", "ranges": [{"startOffset": 9744, "endOffset": 12221, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1931", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/turn.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20810, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20810, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 374, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1062, "endOffset": 1534, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1658, "endOffset": 6066, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1932", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/errorReporting.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13055, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13055, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "reportError", "ranges": [{"startOffset": 1188, "endOffset": 4499, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1933", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/prompts.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 68477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 68477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCoreSystemPrompt", "ranges": [{"startOffset": 2579, "endOffset": 25919, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCompressionPrompt", "ranges": [{"startOffset": 26134, "endOffset": 29221, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1934", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/nextSpeakerChecker.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16737, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16737, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkNextSpeaker", "ranges": [{"startOffset": 3142, "endOffset": 6109, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1935", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/geminiChat.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 69486, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 69486, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "isValidResponse", "ranges": [{"startOffset": 2237, "endOffset": 2536, "count": 0}], "isBlockCoverage": false}, {"functionName": "is<PERSON>alid<PERSON><PERSON>nt", "ranges": [{"startOffset": 2537, "endOffset": 2952, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateHistory", "ranges": [{"startOffset": 3140, "endOffset": 3382, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractCuratedHistory", "ranges": [{"startOffset": 3693, "endOffset": 4789, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4991, "endOffset": 23494, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1936", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/retry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 35868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 35868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultShouldRetry", "ranges": [{"startOffset": 1187, "endOffset": 1726, "count": 0}], "isBlockCoverage": false}, {"functionName": "delay", "ranges": [{"startOffset": 1898, "endOffset": 1982, "count": 0}], "isBlockCoverage": false}, {"functionName": "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2290, "endOffset": 8425, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorStatus", "ranges": [{"startOffset": 8585, "endOffset": 9196, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRetryAfterDelayMs", "ranges": [{"startOffset": 9374, "endOffset": 10582, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDelayDurationAndStatus", "ranges": [{"startOffset": 10801, "endOffset": 11060, "count": 0}], "isBlockCoverage": false}, {"functionName": "logRetryAttempt", "ranges": [{"startOffset": 11305, "endOffset": 12428, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1937", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/quotaErrorDetection.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 390, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 444, "endOffset": 501, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 559, "endOffset": 620, "count": 0}], "isBlockCoverage": false}, {"functionName": "isApiError", "ranges": [{"startOffset": 711, "endOffset": 914, "count": 0}], "isBlockCoverage": false}, {"functionName": "isStructuredError", "ranges": [{"startOffset": 916, "endOffset": 1094, "count": 0}], "isBlockCoverage": false}, {"functionName": "isProQuotaExceededError", "ranges": [{"startOffset": 1096, "endOffset": 3182, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGenericQuotaExceededError", "ranges": [{"startOffset": 3184, "endOffset": 3592, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1938", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/loggers.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28116, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28116, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 442, "endOffset": 487, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 531, "endOffset": 578, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 665, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 710, "endOffset": 758, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldLogUserPrompts", "ranges": [{"startOffset": 2358, "endOffset": 2408, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2410, "endOffset": 2515, "count": 0}], "isBlockCoverage": false}, {"functionName": "logCliConfiguration", "ranges": [{"startOffset": 2516, "endOffset": 3703, "count": 0}], "isBlockCoverage": false}, {"functionName": "logUserPrompt", "ranges": [{"startOffset": 3705, "endOffset": 4473, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCall", "ranges": [{"startOffset": 4475, "endOffset": 5836, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiRequest", "ranges": [{"startOffset": 5838, "endOffset": 6478, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiError", "ranges": [{"startOffset": 6480, "endOffset": 7878, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponse", "ranges": [{"startOffset": 7880, "endOffset": 9900, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1973", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/sdk.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17109, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17109, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 371, "endOffset": 424, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 472, "endOffset": 523, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTelemetrySdkInitialized", "ranges": [{"startOffset": 3850, "endOffset": 3923, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseGrpcEndpoint", "ranges": [{"startOffset": 3925, "endOffset": 4602, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeTelemetry", "ranges": [{"startOffset": 4603, "endOffset": 7042, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdownTelemetry", "ranges": [{"startOffset": 7044, "endOffset": 7473, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2556", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/clearcut-logger/clearcut-logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49427, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49427, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1936, "endOffset": 17139, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1936, "endOffset": 17139, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2557", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23860, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23860, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 3}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 412, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 557, "endOffset": 606, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 652, "endOffset": 701, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 745, "endOffset": 792, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 838, "endOffset": 887, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 931, "endOffset": 978, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1025, "endOffset": 1075, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1536, "endOffset": 1696, "count": 1}], "isBlockCoverage": true}, {"functionName": "getDecisionFromOutcome", "ranges": [{"startOffset": 1744, "endOffset": 2414, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2440, "endOffset": 4133, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4157, "endOffset": 4415, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4439, "endOffset": 4813, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4835, "endOffset": 5608, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5632, "endOffset": 5998, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6020, "endOffset": 6562, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6587, "endOffset": 7724, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2558", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/clearcut-logger/event-metadata-key.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17936, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17936, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 579, "endOffset": 8381, "count": 1}], "isBlockCoverage": true}, {"functionName": "getEventMetadataKey", "ranges": [{"startOffset": 8429, "endOffset": 8728, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2559", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/user_id.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8899, "count": 1}, {"startOffset": 1181, "endOffset": 1186, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 354, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureGeminiDirExists", "ranges": [{"startOffset": 1366, "endOffset": 1537, "count": 0}], "isBlockCoverage": false}, {"functionName": "readInstallationIdFromFile", "ranges": [{"startOffset": 1538, "endOffset": 1809, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeInstallationIdToFile", "ranges": [{"startOffset": 1810, "endOffset": 1950, "count": 0}], "isBlockCoverage": false}, {"functionName": "getInstallationId", "ranges": [{"startOffset": 2136, "endOffset": 2597, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGoogleAccountId", "ranges": [{"startOffset": 2902, "endOffset": 3559, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2560", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/uiTelemetry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16712, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16712, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 408, "count": 0}], "isBlockCoverage": false}, {"functionName": "createInitialModelMetrics", "ranges": [{"startOffset": 1117, "endOffset": 1361, "count": 0}], "isBlockCoverage": false}, {"functionName": "createInitialMetrics", "ranges": [{"startOffset": 1392, "endOffset": 1786, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1834, "endOffset": 5102, "count": 1}], "isBlockCoverage": true}, {"functionName": "addEvent", "ranges": [{"startOffset": 1910, "endOffset": 2619, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMetrics", "ranges": [{"startOffset": 2624, "endOffset": 2674, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLastPromptTokenCount", "ranges": [{"startOffset": 2679, "endOffset": 2755, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOrCreateModelMetrics", "ranges": [{"startOffset": 2760, "endOffset": 2984, "count": 0}], "isBlockCoverage": false}, {"functionName": "processApiResponse", "ranges": [{"startOffset": 2989, "endOffset": 3653, "count": 0}], "isBlockCoverage": false}, {"functionName": "processApiError", "ranges": [{"startOffset": 3658, "endOffset": 3904, "count": 0}], "isBlockCoverage": false}, {"functionName": "processToolCall", "ranges": [{"startOffset": 3909, "endOffset": 5100, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2561", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/tokenLimits.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "tokenLimit", "ranges": [{"startOffset": 524, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2700", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/services/fileDiscoveryService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 922, "endOffset": 3468, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2701", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/gitIgnoreParser.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8136, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8136, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 938, "endOffset": 2808, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2703", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/services/gitService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14614, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14614, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1582, "endOffset": 5470, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2710", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/memoryDiscovery.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30520, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30520, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 265, "endOffset": 327, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 1539, "endOffset": 1603, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1678, "endOffset": 1740, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1816, "endOffset": 1880, "count": 0}], "isBlockCoverage": false}, {"functionName": "findProjectRoot", "ranges": [{"startOffset": 1932, "endOffset": 3025, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGeminiMdFilePathsInternal", "ranges": [{"startOffset": 3026, "endOffset": 6739, "count": 0}], "isBlockCoverage": false}, {"functionName": "readGeminiMdFiles", "ranges": [{"startOffset": 6740, "endOffset": 7792, "count": 0}], "isBlockCoverage": false}, {"functionName": "concatenateInstructions", "ranges": [{"startOffset": 7793, "endOffset": 8432, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadServerHierarchicalMemory", "ranges": [{"startOffset": 8433, "endOffset": 9520, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2711", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/bfsFileSearch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 772, "endOffset": 834, "count": 0}], "isBlockCoverage": false}, {"functionName": "bfsFileSearch", "ranges": [{"startOffset": 1124, "endOffset": 2565, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2712", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/memoryImportProcessor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22070, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22070, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 348, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 830, "endOffset": 894, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 973, "endOffset": 1035, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1115, "endOffset": 1179, "count": 0}], "isBlockCoverage": false}, {"functionName": "processImports", "ranges": [{"startOffset": 1627, "endOffset": 6611, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateImportPath", "ranges": [{"startOffset": 6934, "endOffset": 7386, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2713", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6384, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6384, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 521, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 571, "endOffset": 646, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 694, "endOffset": 767, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 823, "endOffset": 904, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 954, "endOffset": 1029, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1073, "endOffset": 1142, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1184, "endOffset": 1251, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1295, "endOffset": 1364, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1406, "endOffset": 1473, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1518, "endOffset": 1588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1636, "endOffset": 1709, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1826, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1872, "endOffset": 1943, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1987, "endOffset": 2056, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2102, "endOffset": 2173, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2217, "endOffset": 2286, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2405, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2450, "endOffset": 2520, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2560, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2674, "endOffset": 2748, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4190, "endOffset": 4296, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2714", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32748, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32748, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 382, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1088, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1151, "endOffset": 10548, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2715", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/geminiRequest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 0}], "isBlockCoverage": false}, {"functionName": "partListUnionToString", "ranges": [{"startOffset": 404, "endOffset": 1583, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2716", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/coreToolScheduler.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 63100, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 63100, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 420, "count": 0}], "isBlockCoverage": false}, {"functionName": "createFunctionResponsePart", "ranges": [{"startOffset": 1313, "endOffset": 1523, "count": 0}], "isBlockCoverage": false}, {"functionName": "convertToFunctionResponse", "ranges": [{"startOffset": 1524, "endOffset": 3270, "count": 0}], "isBlockCoverage": false}, {"functionName": "createErrorResponse", "ranges": [{"startOffset": 3300, "endOffset": 3577, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3603, "endOffset": 18668, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2717", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/modifiable-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14519, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14519, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModifiableTool", "ranges": [{"startOffset": 1494, "endOffset": 1568, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTempFilesForModify", "ranges": [{"startOffset": 1570, "endOffset": 2615, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUpdatedParams", "ranges": [{"startOffset": 2616, "endOffset": 3645, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteTempFiles", "ranges": [{"startOffset": 3646, "endOffset": 4015, "count": 0}], "isBlockCoverage": false}, {"functionName": "modifyWithEditor", "ranges": [{"startOffset": 4206, "endOffset": 4871, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2718", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/editor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 420, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 519, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 564, "endOffset": 612, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 651, "endOffset": 693, "count": 0}], "isBlockCoverage": false}, {"functionName": "isValidEditorType", "ranges": [{"startOffset": 1033, "endOffset": 1232, "count": 0}], "isBlockCoverage": false}, {"functionName": "commandExists", "ranges": [{"startOffset": 1233, "endOffset": 1453, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkHasEditorType", "ranges": [{"startOffset": 1836, "endOffset": 2056, "count": 0}], "isBlockCoverage": false}, {"functionName": "allowEditorTypeInSandbox", "ranges": [{"startOffset": 2058, "endOffset": 2289, "count": 0}], "isBlockCoverage": false}, {"functionName": "isEditorAvailable", "ranges": [{"startOffset": 2448, "endOffset": 2637, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDiffCommand", "ranges": [{"startOffset": 2694, "endOffset": 4635, "count": 0}], "isBlockCoverage": false}, {"functionName": "openDiff", "ranges": [{"startOffset": 4847, "endOffset": 6752, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2719", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/nonInteractiveToolExecutor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12338, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12338, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "executeToolCall", "ranges": [{"startOffset": 867, "endOffset": 4170, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2720", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/session.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1267, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1267, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2721", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/utils/textUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5436, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5436, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 384, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 473, "count": 335}, {"startOffset": 463, "endOffset": 471, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 509, "endOffset": 548, "count": 536}, {"startOffset": 538, "endOffset": 546, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 586, "endOffset": 627, "count": 112}, {"startOffset": 617, "endOffset": 625, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAsciiArtWidth", "ranges": [{"startOffset": 743, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinary", "ranges": [{"startOffset": 894, "endOffset": 1157, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCodePoints", "ranges": [{"startOffset": 1159, "endOffset": 1215, "count": 983}], "isBlockCoverage": true}, {"functionName": "cpLen", "ranges": [{"startOffset": 1217, "endOffset": 1275, "count": 536}], "isBlockCoverage": true}, {"functionName": "cpSlice", "ranges": [{"startOffset": 1277, "endOffset": 1388, "count": 112}], "isBlockCoverage": true}], "startOffset": 209}]}