{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/components/LoadingIndicator.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28762, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28762, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 421, "endOffset": 1326, "count": 1}], "isBlockCoverage": true}, {"functionName": "GeminiRespondingSpinner", "ranges": [{"startOffset": 457, "endOffset": 1323, "count": 11}, {"startOffset": 662, "endOffset": 967, "count": 9}, {"startOffset": 967, "endOffset": 1301, "count": 2}, {"startOffset": 1301, "endOffset": 1322, "count": 0}], "isBlockCoverage": true}, {"functionName": "renderWithContext", "ranges": [{"startOffset": 2246, "endOffset": 2660, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2695, "endOffset": 12776, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2846, "endOffset": 3269, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3354, "endOffset": 3933, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4048, "endOffset": 4754, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4815, "endOffset": 5358, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5426, "endOffset": 5964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6041, "endOffset": 6583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6635, "endOffset": 7386, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7455, "endOffset": 10491, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10553, "endOffset": 11129, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11180, "endOffset": 11940, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12011, "endOffset": 12772, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1556", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/components/LoadingIndicator.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8471, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8471, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 14}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "LoadingIndicator", "ranges": [{"startOffset": 1384, "endOffset": 4454, "count": 14}, {"startOffset": 1602, "endOffset": 1624, "count": 3}, {"startOffset": 1624, "endOffset": 1655, "count": 11}, {"startOffset": 1655, "endOffset": 1664, "count": 2}, {"startOffset": 1665, "endOffset": 1688, "count": 9}, {"startOffset": 2276, "endOffset": 2281, "count": 2}, {"startOffset": 2282, "endOffset": 2286, "count": 9}, {"startOffset": 2694, "endOffset": 3025, "count": 11}, {"startOffset": 3245, "endOffset": 3249, "count": 2}, {"startOffset": 3250, "endOffset": 3373, "count": 9}, {"startOffset": 3289, "endOffset": 3308, "count": 7}, {"startOffset": 3309, "endOffset": 3370, "count": 2}, {"startOffset": 3837, "endOffset": 4118, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1557", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/colors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5189, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5189, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 22}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "get type", "ranges": [{"startOffset": 522, "endOffset": 614, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Foreground", "ranges": [{"startOffset": 618, "endOffset": 722, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Background", "ranges": [{"startOffset": 726, "endOffset": 830, "count": 0}], "isBlockCoverage": false}, {"functionName": "get LightBlue", "ranges": [{"startOffset": 834, "endOffset": 936, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentBlue", "ranges": [{"startOffset": 940, "endOffset": 1044, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1048, "endOffset": 1156, "count": 11}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON>yan", "ranges": [{"startOffset": 1160, "endOffset": 1264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentGreen", "ranges": [{"startOffset": 1268, "endOffset": 1374, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1378, "endOffset": 1486, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentRed", "ranges": [{"startOffset": 1490, "endOffset": 1592, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Comment", "ranges": [{"startOffset": 1596, "endOffset": 1694, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>", "ranges": [{"startOffset": 1698, "endOffset": 1790, "count": 11}], "isBlockCoverage": true}, {"functionName": "get GradientColors", "ranges": [{"startOffset": 1794, "endOffset": 1906, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1558", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/theme-manager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 386, "count": 22}, {"startOffset": 376, "endOffset": 384, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2407, "endOffset": 4582, "count": 1}], "isBlockCoverage": true}, {"functionName": "ThemeManager", "ranges": [{"startOffset": 2445, "endOffset": 3049, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableThemes", "ranges": [{"startOffset": 3110, "endOffset": 3706, "count": 0}], "isBlockCoverage": false}, {"functionName": "setActiveTheme", "ranges": [{"startOffset": 3877, "endOffset": 4187, "count": 0}], "isBlockCoverage": false}, {"functionName": "findThemeByName", "ranges": [{"startOffset": 4190, "endOffset": 4354, "count": 0}], "isBlockCoverage": false}, {"functionName": "getActiveTheme", "ranges": [{"startOffset": 4417, "endOffset": 4580, "count": 22}, {"startOffset": 4489, "endOffset": 4546, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1559", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/ayu.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8369, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8369, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1560", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27790, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27790, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 30}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 331, "endOffset": 374, "count": 39}, {"startOffset": 364, "endOffset": 372, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 414, "endOffset": 457, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 493, "endOffset": 532, "count": 14}, {"startOffset": 522, "endOffset": 530, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1638, "endOffset": 8558, "count": 14}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1638, "endOffset": 8558, "count": 1}], "isBlockCoverage": true}, {"functionName": "Theme", "ranges": [{"startOffset": 1830, "endOffset": 2173, "count": 14}, {"startOffset": 2114, "endOffset": 2152, "count": 13}, {"startOffset": 2153, "endOffset": 2161, "count": 1}, {"startOffset": 2163, "endOffset": 2168, "count": 1}], "isBlockCoverage": true}, {"functionName": "getInkColor", "ranges": [{"startOffset": 6965, "endOffset": 7031, "count": 0}], "isBlockCoverage": false}, {"functionName": "_resolveColor", "ranges": [{"startOffset": 7312, "endOffset": 7774, "count": 435}, {"startOffset": 7425, "endOffset": 7457, "count": 352}, {"startOffset": 7457, "endOffset": 7640, "count": 83}, {"startOffset": 7508, "endOffset": 7540, "count": 74}, {"startOffset": 7540, "endOffset": 7640, "count": 9}, {"startOffset": 7640, "endOffset": 7773, "count": 0}], "isBlockCoverage": true}, {"functionName": "_buildColorMap", "ranges": [{"startOffset": 8129, "endOffset": 8556, "count": 14}, {"startOffset": 8215, "endOffset": 8530, "count": 508}, {"startOffset": 8252, "endOffset": 8269, "count": 21}, {"startOffset": 8271, "endOffset": 8298, "count": 7}, {"startOffset": 8298, "endOffset": 8358, "count": 501}, {"startOffset": 8360, "endOffset": 8524, "count": 422}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1561", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/ayu-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1562", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/atom-one-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1563", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/dracula.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9249, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9249, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1564", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/github-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1565", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/github-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1566", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/googlecode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1567", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/default-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8651, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8651, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1568", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/default.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 2}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1569", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/shades-of-purple.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26451, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26451, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1570", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/xcode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11217, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11217, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 242, "endOffset": 281, "count": 1}, {"startOffset": 271, "endOffset": 279, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1571", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/ansi.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12794, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12794, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 241, "endOffset": 279, "count": 1}, {"startOffset": 269, "endOffset": 277, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1572", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/ansi-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1573", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/no-color.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1574", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/contexts/StreamingContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2359, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2359, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 25}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 14}, {"startOffset": 396, "endOffset": 404, "count": 0}], "isBlockCoverage": true}, {"functionName": "useStreamingContext", "ranges": [{"startOffset": 769, "endOffset": 1010, "count": 14}, {"startOffset": 881, "endOffset": 989, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1575", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10774, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10774, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 61}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 345, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 439, "endOffset": 487, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 574, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 703, "endOffset": 911, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 975, "endOffset": 1131, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1195, "endOffset": 1505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1565, "endOffset": 1989, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1576", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/utils/formatters.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5378, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5378, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 398, "count": 2}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatMemoryUsage", "ranges": [{"startOffset": 515, "endOffset": 780, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDuration", "ranges": [{"startOffset": 805, "endOffset": 1577, "count": 2}, {"startOffset": 850, "endOffset": 872, "count": 0}, {"startOffset": 900, "endOffset": 949, "count": 0}, {"startOffset": 1019, "endOffset": 1066, "count": 0}, {"startOffset": 1259, "endOffset": 1293, "count": 0}, {"startOffset": 1371, "endOffset": 1407, "count": 1}, {"startOffset": 1435, "endOffset": 1548, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}]}