{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/utils/computeStats.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22248, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22248, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 730, "endOffset": 1593, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 810, "endOffset": 1159, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1238, "endOffset": 1589, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1658, "endOffset": 2542, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1738, "endOffset": 2094, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2178, "endOffset": 2538, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2605, "endOffset": 3482, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2685, "endOffset": 3039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3122, "endOffset": 3478, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3543, "endOffset": 7494, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3635, "endOffset": 4308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4402, "endOffset": 5380, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5461, "endOffset": 6169, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6261, "endOffset": 6734, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6813, "endOffset": 7490, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1023", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/utils/computeStats.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7666, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7666, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 2}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 361, "endOffset": 418, "count": 2}, {"startOffset": 408, "endOffset": 416, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 470, "endOffset": 525, "count": 2}, {"startOffset": 515, "endOffset": 523, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 575, "endOffset": 628, "count": 5}, {"startOffset": 618, "endOffset": 626, "count": 0}], "isBlockCoverage": true}, {"functionName": "calculateErrorRate", "ranges": [{"startOffset": 719, "endOffset": 887, "count": 2}, {"startOffset": 797, "endOffset": 886, "count": 1}], "isBlockCoverage": true}, {"functionName": "calculateAverageLatency", "ranges": [{"startOffset": 889, "endOffset": 1059, "count": 2}, {"startOffset": 972, "endOffset": 1058, "count": 1}], "isBlockCoverage": true}, {"functionName": "calculateCacheHitRate", "ranges": [{"startOffset": 1061, "endOffset": 1222, "count": 2}, {"startOffset": 1138, "endOffset": 1221, "count": 1}], "isBlockCoverage": true}, {"functionName": "computeSessionStats", "ranges": [{"startOffset": 1252, "endOffset": 2581, "count": 5}, {"startOffset": 1568, "endOffset": 1606, "count": 3}, {"startOffset": 1607, "endOffset": 1610, "count": 2}, {"startOffset": 1658, "endOffset": 1697, "count": 3}, {"startOffset": 1698, "endOffset": 1701, "count": 2}, {"startOffset": 1983, "endOffset": 2028, "count": 2}, {"startOffset": 2029, "endOffset": 2032, "count": 3}, {"startOffset": 2191, "endOffset": 2236, "count": 2}, {"startOffset": 2237, "endOffset": 2240, "count": 3}, {"startOffset": 2285, "endOffset": 2337, "count": 1}, {"startOffset": 2338, "endOffset": 2341, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1361, "endOffset": 1407, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1765, "endOffset": 1806, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1881, "endOffset": 1922, "count": 2}], "isBlockCoverage": true}], "startOffset": 209}]}