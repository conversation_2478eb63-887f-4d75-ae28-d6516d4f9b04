{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/types.ts"], "names": [], "mappings": ";;;AA6BA,IAAY,oBAGX;AAHD,WAAY,oBAAoB;IAC9B,qCAAa,CAAA;IACb,qCAAa,CAAA;AACf,CAAC,EAHW,oBAAoB,GAApB,4BAAoB,KAApB,4BAAoB,QAG/B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type * as http from 'http';\nimport type * as https from 'https';\n\nimport { OTLPExporterConfigBase } from '../../types';\n\n/**\n * Collector Exporter node base config\n */\nexport interface OTLPExporterNodeConfigBase extends OTLPExporterConfigBase {\n  keepAlive?: boolean;\n  compression?: CompressionAlgorithm;\n  httpAgentOptions?: http.AgentOptions | https.AgentOptions;\n}\n\nexport enum CompressionAlgorithm {\n  NONE = 'none',\n  GZIP = 'gzip',\n}\n"]}