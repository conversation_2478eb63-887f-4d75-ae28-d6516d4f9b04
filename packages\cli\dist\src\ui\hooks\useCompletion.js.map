{"version": 3, "file": "useCompletion.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useCompletion.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,KAAK,EAAE,MAAM,aAAa,CAAC;AAClC,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAC5B,OAAO,EACL,WAAW,EACX,UAAU,EACV,YAAY,EACZ,eAAe,GAGhB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EACL,uBAAuB,GAExB,MAAM,qCAAqC,CAAC;AAgB7C,MAAM,UAAU,aAAa,CAC3B,KAAa,EACb,GAAW,EACX,QAAiB,EACjB,aAA6B,EAC7B,cAA8B,EAC9B,MAAe;IAEf,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAe,EAAE,CAAC,CAAC;IACjE,MAAM,CAAC,qBAAqB,EAAE,wBAAwB,CAAC,GACrD,QAAQ,CAAS,CAAC,CAAC,CAAC,CAAC;IACvB,MAAM,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,GAAG,QAAQ,CAAS,CAAC,CAAC,CAAC;IACtE,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,QAAQ,CAAU,KAAK,CAAC,CAAC;IACvE,MAAM,CAAC,oBAAoB,EAAE,uBAAuB,CAAC,GACnD,QAAQ,CAAU,KAAK,CAAC,CAAC;IAE3B,MAAM,oBAAoB,GAAG,WAAW,CAAC,GAAG,EAAE;QAC5C,cAAc,CAAC,EAAE,CAAC,CAAC;QACnB,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACxB,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC1B,uBAAuB,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;QAClC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAErC,wBAAwB,CAAC,CAAC,eAAe,EAAE,EAAE;YAC3C,mDAAmD;YACnD,MAAM,cAAc,GAClB,eAAe,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC;YAEtE,uDAAuD;YACvD,oBAAoB,CAAC,CAAC,gBAAgB,EAAE,EAAE;gBACxC,0CAA0C;gBAC1C,IACE,cAAc,KAAK,WAAW,CAAC,MAAM,GAAG,CAAC;oBACzC,WAAW,CAAC,MAAM,GAAG,uBAAuB,EAC5C,CAAC;oBACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,GAAG,uBAAuB,CAAC,CAAC;gBACnE,CAAC;gBACD,oDAAoD;gBACpD,IAAI,cAAc,GAAG,gBAAgB,EAAE,CAAC;oBACtC,OAAO,cAAc,CAAC;gBACxB,CAAC;gBACD,8CAA8C;gBAC9C,OAAO,gBAAgB,CAAC;YAC1B,CAAC,CAAC,CAAC;YAEH,OAAO,cAAc,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;IAEzB,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;QACpC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAErC,wBAAwB,CAAC,CAAC,eAAe,EAAE,EAAE;YAC3C,mDAAmD;YACnD,MAAM,cAAc,GAClB,eAAe,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC;YAEtE,uDAAuD;YACvD,oBAAoB,CAAC,CAAC,gBAAgB,EAAE,EAAE;gBACxC,2CAA2C;gBAC3C,IACE,cAAc,KAAK,CAAC;oBACpB,WAAW,CAAC,MAAM,GAAG,uBAAuB,EAC5C,CAAC;oBACD,OAAO,CAAC,CAAC;gBACX,CAAC;gBACD,oDAAoD;gBACpD,MAAM,eAAe,GAAG,gBAAgB,GAAG,uBAAuB,CAAC;gBACnE,IAAI,cAAc,IAAI,eAAe,EAAE,CAAC;oBACtC,OAAO,cAAc,GAAG,uBAAuB,GAAG,CAAC,CAAC;gBACtD,CAAC;gBACD,8CAA8C;gBAC9C,OAAO,gBAAgB,CAAC;YAC1B,CAAC,CAAC,CAAC;YAEH,OAAO,cAAc,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;IAEzB,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,oBAAoB,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,gBAAgB,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAEpD,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAExD,IAAI,gBAAgB,GAAG,QAAQ,CAAC;YAChC,IAAI,OAAO,GAAG,EAAE,CAAC;YAEjB,gFAAgF;YAChF,8BAA8B;YAC9B,IAAI,CAAC,gBAAgB,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7C,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACxC,gBAAgB,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC;YAED,+DAA+D;YAC/D,IAAI,YAAY,GAA+B,aAAa,CAAC;YAC7D,IAAI,WAAW,GAAwB,IAAI,CAAC;YAE5C,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;gBACpC,IAAI,CAAC,YAAY,EAAE,CAAC;oBAClB,WAAW,GAAG,IAAI,CAAC;oBACnB,YAAY,GAAG,EAAE,CAAC;oBAClB,MAAM;gBACR,CAAC;gBACD,MAAM,KAAK,GAA6B,YAAY,CAAC,IAAI,CACvD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,OAAO,KAAK,IAAI,CACnD,CAAC;gBACF,IAAI,KAAK,EAAE,CAAC;oBACV,WAAW,GAAG,KAAK,CAAC;oBACpB,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC;gBACnC,CAAC;qBAAM,CAAC;oBACN,WAAW,GAAG,IAAI,CAAC;oBACnB,YAAY,GAAG,EAAE,CAAC;oBAClB,MAAM;gBACR,CAAC;YACH,CAAC;YAED,4BAA4B;YAC5B,IAAI,CAAC,gBAAgB,IAAI,YAAY,EAAE,CAAC;gBACtC,MAAM,kBAAkB,GAAG,YAAY,CAAC,IAAI,CAC1C,CAAC,GAAG,EAAE,EAAE,CACN,CAAC,GAAG,CAAC,IAAI,KAAK,OAAO,IAAI,GAAG,CAAC,OAAO,KAAK,OAAO,CAAC;oBACjD,GAAG,CAAC,WAAW,CAClB,CAAC;gBAEF,IAAI,kBAAkB,EAAE,CAAC;oBACvB,yEAAyE;oBACzE,wCAAwC;oBACxC,WAAW,GAAG,kBAAkB,CAAC;oBACjC,YAAY,GAAG,kBAAkB,CAAC,WAAW,CAAC;oBAC9C,OAAO,GAAG,EAAE,CAAC,CAAC,kDAAkD;gBAClE,CAAC;YACH,CAAC;YAED,MAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;YAEtC,yDAAyD;YAEzD,sBAAsB;YACtB,IACE,WAAW,EAAE,UAAU;gBACvB,CAAC,gBAAgB;oBACf,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,IAAI,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,EAAE,CAAC,CAAC,EAC3D,CAAC;gBACD,MAAM,sBAAsB,GAAG,KAAK,IAAI,EAAE;oBACxC,uBAAuB,CAAC,IAAI,CAAC,CAAC;oBAC9B,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAClD,MAAM,OAAO,GACX,CAAC,MAAM,WAAY,CAAC,UAAW,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC;oBACpE,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACtE,cAAc,CAAC,gBAAgB,CAAC,CAAC;oBACjC,kBAAkB,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAChD,wBAAwB,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/D,uBAAuB,CAAC,KAAK,CAAC,CAAC;gBACjC,CAAC,CAAC;gBACF,sBAAsB,EAAE,CAAC;gBACzB,OAAO;YACT,CAAC;YAED,iCAAiC;YACjC,MAAM,gBAAgB,GAAG,YAAY,IAAI,EAAE,CAAC;YAC5C,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,IAAI,oBAAoB,GAAG,gBAAgB,CAAC,MAAM,CAChD,CAAC,GAAG,EAAE,EAAE,CACN,GAAG,CAAC,WAAW;oBACf,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CACrE,CAAC;gBAEF,gEAAgE;gBAChE,mCAAmC;gBACnC,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACzD,MAAM,YAAY,GAAG,oBAAoB,CAAC,IAAI,CAC5C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAC1B,CAAC;oBACF,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;wBAC9C,oBAAoB,GAAG,EAAE,CAAC;oBAC5B,CAAC;gBACH,CAAC;gBAED,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;oBAC1D,KAAK,EAAE,GAAG,CAAC,IAAI;oBACf,KAAK,EAAE,GAAG,CAAC,IAAI;oBACf,WAAW,EAAE,GAAG,CAAC,WAAW;iBAC7B,CAAC,CAAC,CAAC;gBAEJ,cAAc,CAAC,gBAAgB,CAAC,CAAC;gBACjC,kBAAkB,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChD,wBAAwB,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/D,uBAAuB,CAAC,KAAK,CAAC,CAAC;gBAC/B,OAAO;YACT,CAAC;YAED,oDAAoD;YACpD,oBAAoB,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,+BAA+B;QAC/B,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACvC,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC;YACnB,oBAAoB,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACpD,MAAM,eAAe,GACnB,cAAc,KAAK,CAAC,CAAC;YACnB,CAAC,CAAC,GAAG;YACL,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;QACnD,MAAM,MAAM,GAAG,YAAY,CACzB,cAAc,KAAK,CAAC,CAAC;YACnB,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC,CAC9C,CAAC;QAEF,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QAE3D,IAAI,SAAS,GAAG,IAAI,CAAC;QAErB,MAAM,oBAAoB,GAAG,KAAK,EAChC,QAAgB,EAChB,YAAoB,EACpB,aAA0C,EAC1C,aAGC,EACD,mBAAmB,GAAG,EAAE,EACxB,KAAK,GAAG,CAAC,EACT,QAAQ,GAAG,EAAE,EAAE,wBAAwB;QACvC,UAAU,GAAG,EAAE,EACQ,EAAE;YACzB,IAAI,KAAK,GAAG,QAAQ,EAAE,CAAC;gBACrB,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,MAAM,iBAAiB,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;YACrD,IAAI,gBAAgB,GAAiB,EAAE,CAAC;YACxC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;gBACpE,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;oBAC5B,IAAI,gBAAgB,CAAC,MAAM,IAAI,UAAU;wBAAE,MAAM;oBAEjD,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;oBACrE,MAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CACrC,GAAG,EACH,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,CAChC,CAAC;oBAEF,gCAAgC;oBAChC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBAChE,SAAS;oBACX,CAAC;oBAED,6DAA6D;oBAC7D,IACE,aAAa;wBACb,aAAa,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,aAAa,CAAC,EAChE,CAAC;wBACD,SAAS;oBACX,CAAC;oBAED,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,CAAC;wBAC3D,gBAAgB,CAAC,IAAI,CAAC;4BACpB,KAAK,EAAE,iBAAiB,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;4BAC3D,KAAK,EAAE,UAAU,CACf,iBAAiB,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CACrD;yBACF,CAAC,CAAC;oBACL,CAAC;oBACD,IACE,KAAK,CAAC,WAAW,EAAE;wBACnB,KAAK,CAAC,IAAI,KAAK,cAAc;wBAC7B,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAC3B,CAAC;wBACD,IAAI,gBAAgB,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;4BACzC,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CACxC,MAAM,oBAAoB,CACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,EAC/B,YAAY,EAAE,iDAAiD;4BAC/D,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,KAAK,GAAG,CAAC,EACT,QAAQ,EACR,UAAU,GAAG,gBAAgB,CAAC,MAAM,CACrC,CACF,CAAC;wBACJ,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,IAAI,EAAE,CAAC;gBACd,yEAAyE;YAC3E,CAAC;YACD,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QAC/C,CAAC,CAAC;QAEF,MAAM,iBAAiB,GAAG,KAAK,EAC7B,YAAoB,EACpB,oBAA0C,EAC1C,aAGC,EACD,UAAU,GAAG,EAAE,EACQ,EAAE;YACzB,MAAM,WAAW,GAAG,MAAM,YAAY,GAAG,CAAC;YAC1C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE;gBACpC,GAAG;gBACH,GAAG,EAAE,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC;gBACjC,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,MAAM,WAAW,GAAiB,KAAK;iBACpC,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE;gBACpB,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBAC9C,OAAO;oBACL,KAAK,EAAE,YAAY;oBACnB,KAAK,EAAE,UAAU,CAAC,YAAY,CAAC;iBAChC,CAAC;YACJ,CAAC,CAAC;iBACD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;gBACZ,IAAI,oBAAoB,EAAE,CAAC;oBACzB,OAAO,CAAC,oBAAoB,CAAC,gBAAgB,CAC3C,CAAC,CAAC,KAAK,EACP,aAAa,CACd,CAAC,CAAC,gBAAgB;gBACrB,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YAExB,OAAO,WAAW,CAAC;QACrB,CAAC,CAAC;QAEF,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;YAClC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAC9B,IAAI,kBAAkB,GAAiB,EAAE,CAAC;YAE1C,MAAM,oBAAoB,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YACrE,MAAM,qBAAqB,GACzB,MAAM,EAAE,4BAA4B,EAAE,IAAI,IAAI,CAAC;YACjD,MAAM,aAAa,GAAG;gBACpB,gBAAgB,EAAE,MAAM,EAAE,gCAAgC,EAAE,IAAI,IAAI;gBACpE,mBAAmB,EAAE,IAAI;aAC1B,CAAC;YAEF,IAAI,CAAC;gBACH,wEAAwE;gBACxE,IACE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBAC/B,MAAM;oBACN,qBAAqB,EACrB,CAAC;oBACD,IAAI,oBAAoB,EAAE,CAAC;wBACzB,kBAAkB,GAAG,MAAM,iBAAiB,CAC1C,MAAM,EACN,oBAAoB,EACpB,aAAa,CACd,CAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,kBAAkB,GAAG,MAAM,oBAAoB,CAC7C,GAAG,EACH,MAAM,EACN,oBAAoB,EACpB,aAAa,CACd,CAAC;oBACJ,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,0DAA0D;oBAC1D,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;oBACzC,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,eAAe,EAAE;wBAChD,aAAa,EAAE,IAAI;qBACpB,CAAC,CAAC;oBAEH,2CAA2C;oBAC3C,MAAM,eAAe,GAAG,EAAE,CAAC;oBAC3B,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;wBAC5B,gCAAgC;wBAChC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;4BAC1D,SAAS;wBACX,CAAC;wBACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;4BAAE,SAAS;wBAEhE,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAChC,GAAG,EACH,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,CACvC,CAAC;wBACF,IACE,oBAAoB;4BACpB,oBAAoB,CAAC,gBAAgB,CAAC,YAAY,EAAE,aAAa,CAAC,EAClE,CAAC;4BACD,SAAS;wBACX,CAAC;wBAED,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC9B,CAAC;oBAED,kBAAkB,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;wBACjD,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;wBAClE,OAAO;4BACL,KAAK;4BACL,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,oDAAoD;yBAC/E,CAAC;oBACJ,CAAC,CAAC,CAAC;gBACL,CAAC;gBAED,6DAA6D;gBAC7D,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC/B,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;oBACnD,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;oBAEnD,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;wBACtB,OAAO,MAAM,GAAG,MAAM,CAAC;oBACzB,CAAC;oBAED,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oBACrC,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oBACrC,IAAI,MAAM,IAAI,CAAC,MAAM;wBAAE,OAAO,CAAC,CAAC,CAAC;oBACjC,IAAI,CAAC,MAAM,IAAI,MAAM;wBAAE,OAAO,CAAC,CAAC;oBAEhC,OAAO,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC;gBAEH,IAAI,SAAS,EAAE,CAAC;oBACd,cAAc,CAAC,kBAAkB,CAAC,CAAC;oBACnC,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAClD,wBAAwB,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjE,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC;YACH,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACxB,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAClD,IAAI,SAAS,EAAE,CAAC;wBACd,cAAc,CAAC,EAAE,CAAC,CAAC;wBACnB,kBAAkB,CAAC,KAAK,CAAC,CAAC;oBAC5B,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,KAAK,CACX,6CAA6C,WAAW,KAAK,eAAe,CAAC,KAAK,CAAC,EAAE,CACtF,CAAC;oBACF,IAAI,SAAS,EAAE,CAAC;wBACd,oBAAoB,EAAE,CAAC;oBACzB,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,SAAS,EAAE,CAAC;gBACd,uBAAuB,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,UAAU,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;QAE1D,OAAO,GAAG,EAAE;YACV,SAAS,GAAG,KAAK,CAAC;YAClB,YAAY,CAAC,eAAe,CAAC,CAAC;QAChC,CAAC,CAAC;IACJ,CAAC,EAAE;QACD,KAAK;QACL,GAAG;QACH,QAAQ;QACR,oBAAoB;QACpB,aAAa;QACb,cAAc;QACd,MAAM;KACP,CAAC,CAAC;IAEH,OAAO;QACL,WAAW;QACX,qBAAqB;QACrB,iBAAiB;QACjB,eAAe;QACf,oBAAoB;QACpB,wBAAwB;QACxB,kBAAkB;QAClB,oBAAoB;QACpB,UAAU;QACV,YAAY;KACb,CAAC;AACJ,CAAC"}