{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/components/shared/MaxSizedBox.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 58588, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 58588, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1320, "endOffset": 35378, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1471, "endOffset": 2819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2899, "endOffset": 5488, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5611, "endOffset": 8228, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8298, "endOffset": 9687, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9773, "endOffset": 13496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13586, "endOffset": 14986, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15069, "endOffset": 17050, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17142, "endOffset": 19739, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19863, "endOffset": 22488, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22565, "endOffset": 23312, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23405, "endOffset": 24752, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24843, "endOffset": 26203, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26280, "endOffset": 28908, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28979, "endOffset": 31994, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 32075, "endOffset": 33669, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 32148, "endOffset": 32173, "count": 30}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 33552, "endOffset": 33578, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 33753, "endOffset": 35374, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 33826, "endOffset": 33851, "count": 30}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35220, "endOffset": 35245, "count": 9}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1555", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/contexts/OverflowContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 352, "endOffset": 404, "count": 16}, {"startOffset": 394, "endOffset": 402, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 451, "endOffset": 501, "count": 16}, {"startOffset": 491, "endOffset": 499, "count": 0}], "isBlockCoverage": true}, {"functionName": "useOverflowState", "ranges": [{"startOffset": 1146, "endOffset": 1210, "count": 0}], "isBlockCoverage": false}, {"functionName": "useOverflowActions", "ranges": [{"startOffset": 1239, "endOffset": 1305, "count": 16}], "isBlockCoverage": true}, {"functionName": "OverflowProvider", "ranges": [{"startOffset": 1332, "endOffset": 2880, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1528, "endOffset": 1730, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1560, "endOffset": 1724, "count": 8}, {"startOffset": 1602, "endOffset": 1635, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1805, "endOffset": 2011, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1837, "endOffset": 2005, "count": 8}, {"startOffset": 1913, "endOffset": 2004, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2078, "endOffset": 2114, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2204, "endOffset": 2269, "count": 16}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1556", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/components/shared/MaxSizedBox.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45401, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45401, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 361, "endOffset": 418, "count": 1}, {"startOffset": 408, "endOffset": 416, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 505, "count": 16}, {"startOffset": 495, "endOffset": 503, "count": 0}], "isBlockCoverage": true}, {"functionName": "setMaxSizedBoxDebugging", "ranges": [{"startOffset": 1652, "endOffset": 1721, "count": 1}], "isBlockCoverage": true}, {"functionName": "debugReportError", "ranges": [{"startOffset": 1723, "endOffset": 2450, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaxSizedBox", "ranges": [{"startOffset": 2471, "endOffset": 6892, "count": 16}, {"startOffset": 2728, "endOffset": 2733, "count": 0}, {"startOffset": 2828, "endOffset": 2854, "count": 1}, {"startOffset": 2912, "endOffset": 2989, "count": 0}, {"startOffset": 3684, "endOffset": 3717, "count": 8}, {"startOffset": 3770, "endOffset": 3799, "count": 8}, {"startOffset": 3800, "endOffset": 3821, "count": 8}, {"startOffset": 3822, "endOffset": 3839, "count": 8}, {"startOffset": 3963, "endOffset": 3966, "count": 0}, {"startOffset": 4380, "endOffset": 4530, "count": 8}, {"startOffset": 4410, "endOffset": 4479, "count": 5}, {"startOffset": 4480, "endOffset": 4530, "count": 3}, {"startOffset": 4531, "endOffset": 4550, "count": 8}, {"startOffset": 5673, "endOffset": 5703, "count": 8}, {"startOffset": 5704, "endOffset": 6172, "count": 5}, {"startOffset": 5950, "endOffset": 5954, "count": 0}, {"startOffset": 6217, "endOffset": 6250, "count": 8}, {"startOffset": 6251, "endOffset": 6718, "count": 3}, {"startOffset": 6496, "endOffset": 6500, "count": 0}], "isBlockCoverage": true}, {"functionName": "visitRows", "ranges": [{"startOffset": 2993, "endOffset": 3509, "count": 31}, {"startOffset": 3087, "endOffset": 3108, "count": 0}, {"startOffset": 3167, "endOffset": 3277, "count": 1}, {"startOffset": 3277, "endOffset": 3426, "count": 30}, {"startOffset": 3426, "endOffset": 3508, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4080, "endOffset": 4265, "count": 16}, {"startOffset": 4118, "endOffset": 4201, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4214, "endOffset": 4260, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4597, "endOffset": 5485, "count": 56}, {"startOffset": 5052, "endOffset": 5318, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4733, "endOffset": 5050, "count": 70}], "isBlockCoverage": true}, {"functionName": "visitBoxRow", "ranges": [{"startOffset": 6894, "endOffset": 9726, "count": 30}, {"startOffset": 7034, "endOffset": 7237, "count": 0}, {"startOffset": 7447, "endOffset": 7482, "count": 0}, {"startOffset": 7484, "endOffset": 7603, "count": 0}, {"startOffset": 7662, "endOffset": 7842, "count": 0}], "isBlockCoverage": true}, {"functionName": "visitRowChild", "ranges": [{"startOffset": 7942, "endOffset": 9583, "count": 64}, {"startOffset": 8017, "endOffset": 8038, "count": 0}, {"startOffset": 8077, "endOffset": 8108, "count": 32}, {"startOffset": 8110, "endOffset": 8736, "count": 32}, {"startOffset": 8166, "endOffset": 8191, "count": 0}, {"startOffset": 8242, "endOffset": 8247, "count": 0}, {"startOffset": 8316, "endOffset": 8392, "count": 6}, {"startOffset": 8392, "endOffset": 8715, "count": 26}, {"startOffset": 8484, "endOffset": 8707, "count": 0}, {"startOffset": 8736, "endOffset": 8803, "count": 32}, {"startOffset": 8803, "endOffset": 8878, "count": 0}, {"startOffset": 8878, "endOffset": 8938, "count": 32}, {"startOffset": 8938, "endOffset": 9108, "count": 0}, {"startOffset": 9108, "endOffset": 9164, "count": 32}, {"startOffset": 9164, "endOffset": 9293, "count": 0}, {"startOffset": 9293, "endOffset": 9414, "count": 32}, {"startOffset": 9415, "endOffset": 9452, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9035, "endOffset": 9079, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9528, "endOffset": 9572, "count": 32}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9666, "endOffset": 9705, "count": 32}], "isBlockCoverage": true}, {"functionName": "layoutInkElementAsStyledText", "ranges": [{"startOffset": 9727, "endOffset": 13805, "count": 30}, {"startOffset": 9862, "endOffset": 9896, "count": 24}, {"startOffset": 9898, "endOffset": 9936, "count": 0}, {"startOffset": 10211, "endOffset": 10868, "count": 24}, {"startOffset": 10640, "endOffset": 10745, "count": 0}, {"startOffset": 10819, "endOffset": 10851, "count": 82}, {"startOffset": 10868, "endOffset": 10949, "count": 6}, {"startOffset": 10949, "endOffset": 11003, "count": 0}, {"startOffset": 11003, "endOffset": 13775, "count": 6}, {"startOffset": 13775, "endOffset": 13803, "count": 29}, {"startOffset": 13803, "endOffset": 13804, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10047, "endOffset": 10175, "count": 26}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10293, "endOffset": 10606, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10382, "endOffset": 10598, "count": 82}, {"startOffset": 10424, "endOffset": 10499, "count": 58}], "isBlockCoverage": true}, {"functionName": "addWrappingPartToLines", "ranges": [{"startOffset": 11061, "endOffset": 11474, "count": 29}, {"startOffset": 11125, "endOffset": 11192, "count": 6}, {"startOffset": 11192, "endOffset": 11419, "count": 23}, {"startOffset": 11231, "endOffset": 11364, "count": 12}, {"startOffset": 11364, "endOffset": 11413, "count": 11}], "isBlockCoverage": true}, {"functionName": "addToWrappingPart", "ranges": [{"startOffset": 11477, "endOffset": 11733, "count": 55}, {"startOffset": 11551, "endOffset": 11607, "count": 26}, {"startOffset": 11609, "endOffset": 11674, "count": 26}, {"startOffset": 11674, "endOffset": 11729, "count": 29}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11757, "endOffset": 13676, "count": 6}, {"startOffset": 13633, "endOffset": 13672, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11856, "endOffset": 13593, "count": 8}, {"startOffset": 11908, "endOffset": 11951, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12018, "endOffset": 13585, "count": 50}, {"startOffset": 12049, "endOffset": 12056, "count": 1}, {"startOffset": 12056, "endOffset": 12183, "count": 49}, {"startOffset": 12183, "endOffset": 12207, "count": 15}, {"startOffset": 12209, "endOffset": 12324, "count": 11}, {"startOffset": 12281, "endOffset": 12314, "count": 4}, {"startOffset": 12324, "endOffset": 12366, "count": 45}, {"startOffset": 12366, "endOffset": 13468, "count": 4}, {"startOffset": 12564, "endOffset": 13458, "count": 14}, {"startOffset": 12695, "endOffset": 12985, "count": 60}, {"startOffset": 12856, "endOffset": 12896, "count": 10}, {"startOffset": 12896, "endOffset": 12985, "count": 50}, {"startOffset": 13391, "endOffset": 13446, "count": 10}, {"startOffset": 13468, "endOffset": 13577, "count": 41}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1559", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/colors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5189, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5189, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 8}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "get type", "ranges": [{"startOffset": 522, "endOffset": 614, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Foreground", "ranges": [{"startOffset": 618, "endOffset": 722, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Background", "ranges": [{"startOffset": 726, "endOffset": 830, "count": 0}], "isBlockCoverage": false}, {"functionName": "get LightBlue", "ranges": [{"startOffset": 834, "endOffset": 936, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentBlue", "ranges": [{"startOffset": 940, "endOffset": 1044, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1048, "endOffset": 1156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON>yan", "ranges": [{"startOffset": 1160, "endOffset": 1264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentGreen", "ranges": [{"startOffset": 1268, "endOffset": 1374, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1378, "endOffset": 1486, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentRed", "ranges": [{"startOffset": 1490, "endOffset": 1592, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Comment", "ranges": [{"startOffset": 1596, "endOffset": 1694, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>", "ranges": [{"startOffset": 1698, "endOffset": 1790, "count": 8}], "isBlockCoverage": true}, {"functionName": "get GradientColors", "ranges": [{"startOffset": 1794, "endOffset": 1906, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1560", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/theme-manager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 386, "count": 8}, {"startOffset": 376, "endOffset": 384, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2407, "endOffset": 4582, "count": 1}], "isBlockCoverage": true}, {"functionName": "ThemeManager", "ranges": [{"startOffset": 2445, "endOffset": 3049, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableThemes", "ranges": [{"startOffset": 3110, "endOffset": 3706, "count": 0}], "isBlockCoverage": false}, {"functionName": "setActiveTheme", "ranges": [{"startOffset": 3877, "endOffset": 4187, "count": 0}], "isBlockCoverage": false}, {"functionName": "findThemeByName", "ranges": [{"startOffset": 4190, "endOffset": 4354, "count": 0}], "isBlockCoverage": false}, {"functionName": "getActiveTheme", "ranges": [{"startOffset": 4417, "endOffset": 4580, "count": 8}, {"startOffset": 4489, "endOffset": 4546, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1561", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/ayu.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8369, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8369, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1562", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27790, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27790, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 30}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 331, "endOffset": 374, "count": 39}, {"startOffset": 364, "endOffset": 372, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 414, "endOffset": 457, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 493, "endOffset": 532, "count": 14}, {"startOffset": 522, "endOffset": 530, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1638, "endOffset": 8558, "count": 14}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1638, "endOffset": 8558, "count": 1}], "isBlockCoverage": true}, {"functionName": "Theme", "ranges": [{"startOffset": 1830, "endOffset": 2173, "count": 14}, {"startOffset": 2114, "endOffset": 2152, "count": 13}, {"startOffset": 2153, "endOffset": 2161, "count": 1}, {"startOffset": 2163, "endOffset": 2168, "count": 1}], "isBlockCoverage": true}, {"functionName": "getInkColor", "ranges": [{"startOffset": 6965, "endOffset": 7031, "count": 0}], "isBlockCoverage": false}, {"functionName": "_resolveColor", "ranges": [{"startOffset": 7312, "endOffset": 7774, "count": 435}, {"startOffset": 7425, "endOffset": 7457, "count": 352}, {"startOffset": 7457, "endOffset": 7640, "count": 83}, {"startOffset": 7508, "endOffset": 7540, "count": 74}, {"startOffset": 7540, "endOffset": 7640, "count": 9}, {"startOffset": 7640, "endOffset": 7773, "count": 0}], "isBlockCoverage": true}, {"functionName": "_buildColorMap", "ranges": [{"startOffset": 8129, "endOffset": 8556, "count": 14}, {"startOffset": 8215, "endOffset": 8530, "count": 508}, {"startOffset": 8252, "endOffset": 8269, "count": 21}, {"startOffset": 8271, "endOffset": 8298, "count": 7}, {"startOffset": 8298, "endOffset": 8358, "count": 501}, {"startOffset": 8360, "endOffset": 8524, "count": 422}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1563", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/ayu-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1564", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/atom-one-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1565", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/dracula.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9249, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9249, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1566", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/github-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1567", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/github-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1568", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/googlecode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1569", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/default-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8651, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8651, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1570", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/default.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 2}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1571", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/shades-of-purple.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26451, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26451, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1572", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/xcode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11217, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11217, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 242, "endOffset": 281, "count": 1}, {"startOffset": 271, "endOffset": 279, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1573", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/ansi.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12794, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12794, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 241, "endOffset": 279, "count": 1}, {"startOffset": 269, "endOffset": 277, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1574", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/ansi-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1575", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/no-color.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1576", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/utils/textUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5436, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5436, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 384, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 473, "count": 4}, {"startOffset": 463, "endOffset": 471, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 509, "endOffset": 548, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 586, "endOffset": 627, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAsciiArtWidth", "ranges": [{"startOffset": 743, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinary", "ranges": [{"startOffset": 894, "endOffset": 1157, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCodePoints", "ranges": [{"startOffset": 1159, "endOffset": 1215, "count": 4}], "isBlockCoverage": true}, {"functionName": "cpLen", "ranges": [{"startOffset": 1217, "endOffset": 1275, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpSlice", "ranges": [{"startOffset": 1277, "endOffset": 1388, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}