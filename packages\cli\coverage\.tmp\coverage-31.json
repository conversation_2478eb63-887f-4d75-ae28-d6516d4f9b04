{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/config/extension.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16215, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16215, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 395, "endOffset": 533, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1027, "endOffset": 3211, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1091, "endOffset": 1467, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1482, "endOffset": 1648, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1715, "endOffset": 2480, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2211, "endOffset": 2242, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2278, "endOffset": 2309, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2547, "endOffset": 3207, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3040, "endOffset": 3071, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3249, "endOffset": 4901, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3569, "endOffset": 3711, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3764, "endOffset": 4096, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3968, "endOffset": 3999, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4047, "endOffset": 4078, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4159, "endOffset": 4307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4351, "endOffset": 4557, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4611, "endOffset": 4897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4708, "endOffset": 4721, "count": 4}], "isBlockCoverage": true}, {"functionName": "createExtension", "ranges": [{"startOffset": 4904, "endOffset": 5511, "count": 3}, {"startOffset": 5292, "endOffset": 5386, "count": 1}, {"startOffset": 5411, "endOffset": 5509, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1023", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/config/extension.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 2}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 378, "endOffset": 438, "count": 3}, {"startOffset": 428, "endOffset": 436, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 483, "endOffset": 531, "count": 2}, {"startOffset": 521, "endOffset": 529, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 584, "endOffset": 640, "count": 5}, {"startOffset": 630, "endOffset": 638, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadExtensions", "ranges": [{"startOffset": 1159, "endOffset": 1732, "count": 2}, {"startOffset": 1431, "endOffset": 1681, "count": 3}], "isBlockCoverage": true}, {"functionName": "loadExtensionsFromDir", "ranges": [{"startOffset": 1734, "endOffset": 2257, "count": 4}, {"startOffset": 1912, "endOffset": 2032, "count": 2}, {"startOffset": 2032, "endOffset": 2233, "count": 3}, {"startOffset": 2233, "endOffset": 2256, "count": 2}], "isBlockCoverage": true}, {"functionName": "loadExtension", "ranges": [{"startOffset": 2258, "endOffset": 3524, "count": 3}, {"startOffset": 2364, "endOffset": 2486, "count": 0}, {"startOffset": 2640, "endOffset": 2791, "count": 0}, {"startOffset": 2975, "endOffset": 3113, "count": 0}, {"startOffset": 3389, "endOffset": 3522, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3172, "endOffset": 3250, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3259, "endOffset": 3329, "count": 3}], "isBlockCoverage": true}, {"functionName": "getContextFileNames", "ranges": [{"startOffset": 3525, "endOffset": 3754, "count": 3}, {"startOffset": 3595, "endOffset": 3626, "count": 2}, {"startOffset": 3626, "endOffset": 3718, "count": 1}, {"startOffset": 3718, "endOffset": 3753, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterActiveExtensions", "ranges": [{"startOffset": 3755, "endOffset": 4895, "count": 5}, {"startOffset": 3866, "endOffset": 3894, "count": 1}, {"startOffset": 3894, "endOffset": 4052, "count": 4}, {"startOffset": 4052, "endOffset": 4093, "count": 3}, {"startOffset": 4095, "endOffset": 4206, "count": 1}, {"startOffset": 4206, "endOffset": 4338, "count": 3}, {"startOffset": 4338, "endOffset": 4755, "count": 9}, {"startOffset": 4458, "endOffset": 4671, "count": 3}, {"startOffset": 4671, "endOffset": 4751, "count": 6}, {"startOffset": 4755, "endOffset": 4802, "count": 3}, {"startOffset": 4802, "endOffset": 4865, "count": 1}, {"startOffset": 4865, "endOffset": 4894, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3972, "endOffset": 4001, "count": 5}], "isBlockCoverage": true}], "startOffset": 209}]}