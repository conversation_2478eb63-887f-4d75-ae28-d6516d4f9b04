{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/components/messages/ToolMessage.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 25136, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 25136, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 423, "endOffset": 1300, "count": 1}], "isBlockCoverage": true}, {"functionName": "GeminiRespondingSpinner", "ranges": [{"startOffset": 459, "endOffset": 1297, "count": 3}, {"startOffset": 664, "endOffset": 973, "count": 1}, {"startOffset": 973, "endOffset": 1285, "count": 2}, {"startOffset": 1286, "endOffset": 1292, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1354, "endOffset": 1748, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1379, "endOffset": 1745, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1815, "endOffset": 2199, "count": 1}], "isBlockCoverage": true}, {"functionName": "MockMarkdownDisplay", "ranges": [{"startOffset": 1843, "endOffset": 2196, "count": 11}], "isBlockCoverage": true}, {"functionName": "renderWithContext", "ranges": [{"startOffset": 3122, "endOffset": 3530, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3560, "endOffset": 10974, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3885, "endOffset": 4490, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4537, "endOffset": 9342, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4582, "endOffset": 5075, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5115, "endOffset": 5608, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5651, "endOffset": 6147, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6188, "endOffset": 6683, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6721, "endOffset": 7213, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7296, "endOffset": 7904, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8005, "endOffset": 8631, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8727, "endOffset": 9336, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9391, "endOffset": 10000, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10038, "endOffset": 10970, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1556", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/components/messages/ToolMessage.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24612, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24612, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 12}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "ToolMessage", "ranges": [{"startOffset": 1828, "endOffset": 7177, "count": 12}, {"startOffset": 2038, "endOffset": 2179, "count": 0}, {"startOffset": 2213, "endOffset": 2254, "count": 0}, {"startOffset": 2337, "endOffset": 2503, "count": 11}, {"startOffset": 2405, "endOffset": 2499, "count": 0}, {"startOffset": 3469, "endOffset": 3726, "count": 1}, {"startOffset": 4248, "endOffset": 4273, "count": 11}, {"startOffset": 4274, "endOffset": 5078, "count": 11}, {"startOffset": 5120, "endOffset": 5146, "count": 11}, {"startOffset": 5147, "endOffset": 6070, "count": 0}, {"startOffset": 6112, "endOffset": 6642, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToolStatusIndicator", "ranges": [{"startOffset": 7207, "endOffset": 9867, "count": 12}, {"startOffset": 7411, "endOffset": 7729, "count": 1}, {"startOffset": 7791, "endOffset": 8166, "count": 3}, {"startOffset": 8226, "endOffset": 8544, "count": 5}, {"startOffset": 8607, "endOffset": 8926, "count": 1}, {"startOffset": 8987, "endOffset": 9318, "count": 1}, {"startOffset": 9376, "endOffset": 9704, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToolInfo", "ranges": [{"startOffset": 9886, "endOffset": 11765, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9999, "endOffset": 10359, "count": 12}, {"startOffset": 10037, "endOffset": 10105, "count": 1}, {"startOffset": 10112, "endOffset": 10182, "count": 10}, {"startOffset": 10189, "endOffset": 10250, "count": 1}, {"startOffset": 10257, "endOffset": 10349, "count": 0}], "isBlockCoverage": true}, {"functionName": "TrailingIndicator", "ranges": [{"startOffset": 11793, "endOffset": 12135, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1557", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10774, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10774, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 15}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 345, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 439, "endOffset": 487, "count": 93}, {"startOffset": 477, "endOffset": 485, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 574, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 703, "endOffset": 911, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 975, "endOffset": 1131, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1195, "endOffset": 1505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1565, "endOffset": 1989, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1558", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/colors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5189, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5189, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 34}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "get type", "ranges": [{"startOffset": 522, "endOffset": 614, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Foreground", "ranges": [{"startOffset": 618, "endOffset": 722, "count": 12}], "isBlockCoverage": true}, {"functionName": "get Background", "ranges": [{"startOffset": 726, "endOffset": 830, "count": 0}], "isBlockCoverage": false}, {"functionName": "get LightBlue", "ranges": [{"startOffset": 834, "endOffset": 936, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentBlue", "ranges": [{"startOffset": 940, "endOffset": 1044, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1048, "endOffset": 1156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get Accent<PERSON>yan", "ranges": [{"startOffset": 1160, "endOffset": 1264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get AccentGreen", "ranges": [{"startOffset": 1268, "endOffset": 1374, "count": 6}], "isBlockCoverage": true}, {"functionName": "get Accent<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1378, "endOffset": 1486, "count": 2}], "isBlockCoverage": true}, {"functionName": "get AccentRed", "ranges": [{"startOffset": 1490, "endOffset": 1592, "count": 1}], "isBlockCoverage": true}, {"functionName": "get Comment", "ranges": [{"startOffset": 1596, "endOffset": 1694, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>", "ranges": [{"startOffset": 1698, "endOffset": 1790, "count": 13}], "isBlockCoverage": true}, {"functionName": "get GradientColors", "ranges": [{"startOffset": 1794, "endOffset": 1906, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1559", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/theme-manager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 386, "count": 34}, {"startOffset": 376, "endOffset": 384, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2407, "endOffset": 4582, "count": 1}], "isBlockCoverage": true}, {"functionName": "ThemeManager", "ranges": [{"startOffset": 2445, "endOffset": 3049, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableThemes", "ranges": [{"startOffset": 3110, "endOffset": 3706, "count": 0}], "isBlockCoverage": false}, {"functionName": "setActiveTheme", "ranges": [{"startOffset": 3877, "endOffset": 4187, "count": 0}], "isBlockCoverage": false}, {"functionName": "findThemeByName", "ranges": [{"startOffset": 4190, "endOffset": 4354, "count": 0}], "isBlockCoverage": false}, {"functionName": "getActiveTheme", "ranges": [{"startOffset": 4417, "endOffset": 4580, "count": 34}, {"startOffset": 4489, "endOffset": 4546, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1560", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/ayu.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8369, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8369, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1561", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27790, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27790, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 30}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 331, "endOffset": 374, "count": 39}, {"startOffset": 364, "endOffset": 372, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 414, "endOffset": 457, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 493, "endOffset": 532, "count": 14}, {"startOffset": 522, "endOffset": 530, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1638, "endOffset": 8558, "count": 14}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1638, "endOffset": 8558, "count": 1}], "isBlockCoverage": true}, {"functionName": "Theme", "ranges": [{"startOffset": 1830, "endOffset": 2173, "count": 14}, {"startOffset": 2114, "endOffset": 2152, "count": 13}, {"startOffset": 2153, "endOffset": 2161, "count": 1}, {"startOffset": 2163, "endOffset": 2168, "count": 1}], "isBlockCoverage": true}, {"functionName": "getInkColor", "ranges": [{"startOffset": 6965, "endOffset": 7031, "count": 0}], "isBlockCoverage": false}, {"functionName": "_resolveColor", "ranges": [{"startOffset": 7312, "endOffset": 7774, "count": 435}, {"startOffset": 7425, "endOffset": 7457, "count": 352}, {"startOffset": 7457, "endOffset": 7640, "count": 83}, {"startOffset": 7508, "endOffset": 7540, "count": 74}, {"startOffset": 7540, "endOffset": 7640, "count": 9}, {"startOffset": 7640, "endOffset": 7773, "count": 0}], "isBlockCoverage": true}, {"functionName": "_buildColorMap", "ranges": [{"startOffset": 8129, "endOffset": 8556, "count": 14}, {"startOffset": 8215, "endOffset": 8530, "count": 508}, {"startOffset": 8252, "endOffset": 8269, "count": 21}, {"startOffset": 8271, "endOffset": 8298, "count": 7}, {"startOffset": 8298, "endOffset": 8358, "count": 501}, {"startOffset": 8360, "endOffset": 8524, "count": 422}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1562", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/ayu-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1563", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/atom-one-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1564", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/dracula.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9249, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9249, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 244, "endOffset": 285, "count": 1}, {"startOffset": 275, "endOffset": 283, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1565", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/github-dark.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1566", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/github-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11498, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1567", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/googlecode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1568", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/default-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8651, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8651, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1569", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/default.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 2}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1570", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/shades-of-purple.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26451, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26451, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1571", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/xcode.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11217, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11217, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 242, "endOffset": 281, "count": 1}, {"startOffset": 271, "endOffset": 279, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1572", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/ansi.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12794, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12794, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 241, "endOffset": 279, "count": 1}, {"startOffset": 269, "endOffset": 277, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1573", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/ansi-light.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1574", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/themes/no-color.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1575", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/components/shared/MaxSizedBox.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45401, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45401, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 361, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 505, "count": 0}], "isBlockCoverage": false}, {"functionName": "setMaxSizedBoxDebugging", "ranges": [{"startOffset": 1652, "endOffset": 1721, "count": 0}], "isBlockCoverage": false}, {"functionName": "debugReportError", "ranges": [{"startOffset": 1723, "endOffset": 2450, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaxSizedBox", "ranges": [{"startOffset": 2471, "endOffset": 6892, "count": 0}], "isBlockCoverage": false}, {"functionName": "visitBoxRow", "ranges": [{"startOffset": 6894, "endOffset": 9726, "count": 0}], "isBlockCoverage": false}, {"functionName": "layoutInkElementAsStyledText", "ranges": [{"startOffset": 9727, "endOffset": 13805, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1578", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/utils/textUtils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5436, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5436, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 384, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 473, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 509, "endOffset": 548, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 586, "endOffset": 627, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAsciiArtWidth", "ranges": [{"startOffset": 743, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinary", "ranges": [{"startOffset": 894, "endOffset": 1157, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCodePoints", "ranges": [{"startOffset": 1159, "endOffset": 1215, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpLen", "ranges": [{"startOffset": 1217, "endOffset": 1275, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpSlice", "ranges": [{"startOffset": 1277, "endOffset": 1388, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1579", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/contexts/OverflowContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 352, "endOffset": 404, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 451, "endOffset": 501, "count": 0}], "isBlockCoverage": false}, {"functionName": "useOverflowState", "ranges": [{"startOffset": 1146, "endOffset": 1210, "count": 0}], "isBlockCoverage": false}, {"functionName": "useOverflowActions", "ranges": [{"startOffset": 1239, "endOffset": 1305, "count": 0}], "isBlockCoverage": false}, {"functionName": "OverflowProvider", "ranges": [{"startOffset": 1332, "endOffset": 2880, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1580", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/contexts/StreamingContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2359, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2359, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 15}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "useStreamingContext", "ranges": [{"startOffset": 769, "endOffset": 1010, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}