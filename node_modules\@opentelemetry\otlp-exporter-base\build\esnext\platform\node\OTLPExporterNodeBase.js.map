{"version": 3, "file": "OTLPExporterNodeBase.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/OTLPExporterNodeBase.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAKH,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAC;AAG1D,OAAO,EAAE,YAAY,EAAE,MAAM,YAAY,CAAC;AAC1C,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,oBAAoB,EAAE,MAAM,QAAQ,CAAC;AAC7E,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AAG3D;;GAEG;AACH,MAAM,OAAgB,oBAGpB,SAAQ,gBAAwD;IAQhE,YACE,SAAqC,EAAE,EACvC,UAAsD,EACtD,WAAmB;QAEnB,KAAK,CAAC,MAAM,CAAC,CAAC;QAZhB,oBAAe,GAA2B,EAAE,CAAC;QAa3C,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,8DAA8D;QAC9D,IAAK,MAAc,CAAC,QAAQ,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;SACrD;QACD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAC1B,IAAI,CAAC,eAAe,EACpB,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,EAC5B,YAAY,CAAC,uBAAuB,CAAC,MAAM,EAAE,CAAC,0BAA0B,CAAC,CAC1E,CAAC;QACF,IAAI,CAAC,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC5D,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,OAAmC,IAAS,CAAC;IAEpD,IAAI,CACF,OAAqB,EACrB,SAAqB,EACrB,OAAqD;QAErD,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC5D,OAAO;SACR;QAED,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;;YACpD,YAAY,CACV,IAAI,EACJ,MAAA,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,CAAC,mCAAI,IAAI,UAAU,EAAE,EAC9D,IAAI,CAAC,YAAY,EACjB,OAAO,EACP,MAAM,CACP,CAAC;QACJ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,GAAG,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACvC,CAAC;IAED,UAAU,KAAU,CAAC;CACtB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as http from 'http';\nimport type * as https from 'https';\n\nimport { OTLPExporterBase } from '../../OTLPExporterBase';\nimport { OTLPExporterNodeConfigBase, CompressionAlgorithm } from './types';\nimport * as otlpTypes from '../../types';\nimport { parseHeaders } from '../../util';\nimport { createHttpAgent, sendWithHttp, configureCompression } from './util';\nimport { diag } from '@opentelemetry/api';\nimport { getEnv, baggageUtils } from '@opentelemetry/core';\nimport { ISerializer } from '@opentelemetry/otlp-transformer';\n\n/**\n * Collector Metric Exporter abstract base class\n */\nexport abstract class OTLPExporterNodeBase<\n  ExportItem,\n  ServiceResponse,\n> extends OTLPExporterBase<OTLPExporterNodeConfigBase, ExportItem> {\n  DEFAULT_HEADERS: Record<string, string> = {};\n  headers: Record<string, string>;\n  agent: http.Agent | https.Agent | undefined;\n  compression: CompressionAlgorithm;\n  private _serializer: ISerializer<ExportItem[], ServiceResponse>;\n  private _contentType: string;\n\n  constructor(\n    config: OTLPExporterNodeConfigBase = {},\n    serializer: ISerializer<ExportItem[], ServiceResponse>,\n    contentType: string\n  ) {\n    super(config);\n    this._contentType = contentType;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    if ((config as any).metadata) {\n      diag.warn('Metadata cannot be set when using http');\n    }\n    this.headers = Object.assign(\n      this.DEFAULT_HEADERS,\n      parseHeaders(config.headers),\n      baggageUtils.parseKeyPairsIntoRecord(getEnv().OTEL_EXPORTER_OTLP_HEADERS)\n    );\n    this.agent = createHttpAgent(config);\n    this.compression = configureCompression(config.compression);\n    this._serializer = serializer;\n  }\n\n  onInit(_config: OTLPExporterNodeConfigBase): void {}\n\n  send(\n    objects: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: otlpTypes.OTLPExporterError) => void\n  ): void {\n    if (this._shutdownOnce.isCalled) {\n      diag.debug('Shutdown already started. Cannot send objects');\n      return;\n    }\n\n    const promise = new Promise<void>((resolve, reject) => {\n      sendWithHttp(\n        this,\n        this._serializer.serializeRequest(objects) ?? new Uint8Array(),\n        this._contentType,\n        resolve,\n        reject\n      );\n    }).then(onSuccess, onError);\n\n    this._sendingPromises.push(promise);\n    const popPromise = () => {\n      const index = this._sendingPromises.indexOf(promise);\n      this._sendingPromises.splice(index, 1);\n    };\n    promise.then(popPromise, popPromise);\n  }\n\n  onShutdown(): void {}\n}\n"]}