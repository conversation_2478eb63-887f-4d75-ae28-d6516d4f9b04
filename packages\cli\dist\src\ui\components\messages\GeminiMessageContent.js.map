{"version": 3, "file": "GeminiMessageContent.js", "sourceRoot": "", "sources": ["../../../../../src/ui/components/messages/GeminiMessageContent.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC1B,OAAO,EAAE,eAAe,EAAE,MAAM,gCAAgC,CAAC;AASjE;;;;;GAKG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAwC,CAAC,EACxE,IAAI,EACJ,SAAS,EACT,uBAAuB,EACvB,aAAa,GACd,EAAE,EAAE;IACH,MAAM,cAAc,GAAG,IAAI,CAAC;IAC5B,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,CAAC;IAE1C,OAAO,CACL,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,WAAW,EAAE,WAAW,YAClD,KAAC,eAAe,IACd,IAAI,EAAE,IAAI,EACV,SAAS,EAAE,SAAS,EACpB,uBAAuB,EAAE,uBAAuB,EAChD,aAAa,EAAE,aAAa,GAC5B,GACE,CACP,CAAC;AACJ,CAAC,CAAC"}