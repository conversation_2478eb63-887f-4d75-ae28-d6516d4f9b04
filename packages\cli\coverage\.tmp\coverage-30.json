{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/shellCommandProcessor.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16437, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16437, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 480, "endOffset": 613, "count": 1}], "isBlockCoverage": true}, {"functionName": "platform", "ranges": [{"startOffset": 516, "endOffset": 529, "count": 6}], "isBlockCoverage": true}, {"functionName": "tmpdir", "ranges": [{"startOffset": 543, "endOffset": 555, "count": 3}], "isBlockCoverage": true}, {"functionName": "platform", "ranges": [{"startOffset": 573, "endOffset": 586, "count": 0}], "isBlockCoverage": false}, {"functionName": "tmpdir", "ranges": [{"startOffset": 598, "endOffset": 610, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 774, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1313, "endOffset": 5591, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1501, "endOffset": 2465, "count": 3}], "isBlockCoverage": true}, {"functionName": "getTargetDir", "ranges": [{"startOffset": 2357, "endOffset": 2374, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2480, "endOffset": 2539, "count": 3}], "isBlockCoverage": true}, {"functionName": "renderProcessorHook", "ranges": [{"startOffset": 2572, "endOffset": 2813, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2610, "endOffset": 2809, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2878, "endOffset": 3732, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3013, "endOffset": 3100, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3229, "endOffset": 3319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3346, "endOffset": 3401, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3434, "endOffset": 3478, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3771, "endOffset": 4747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4032, "endOffset": 4153, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4282, "endOffset": 4367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4394, "endOffset": 4449, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4482, "endOffset": 4526, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4788, "endOffset": 5587, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4923, "endOffset": 5042, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5122, "endOffset": 5209, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5236, "endOffset": 5293, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5326, "endOffset": 5370, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1321", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/shellCommandProcessor.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32520, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32520, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 3}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "executeShellCommand", "ranges": [{"startOffset": 1936, "endOffset": 5303, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2055, "endOffset": 5299, "count": 3}, {"startOffset": 2174, "endOffset": 2185, "count": 0}, {"startOffset": 2228, "endOffset": 2254, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleOutput", "ranges": [{"startOffset": 2810, "endOffset": 3970, "count": 3}, {"startOffset": 3092, "endOffset": 3205, "count": 1}, {"startOffset": 3262, "endOffset": 3289, "count": 2}, {"startOffset": 3290, "endOffset": 3317, "count": 1}, {"startOffset": 3350, "endOffset": 3426, "count": 2}, {"startOffset": 3426, "endOffset": 3508, "count": 1}, {"startOffset": 3543, "endOffset": 3661, "count": 2}, {"startOffset": 3593, "endOffset": 3607, "count": 1}, {"startOffset": 3608, "endOffset": 3612, "count": 1}, {"startOffset": 3661, "endOffset": 3964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3755, "endOffset": 3789, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4000, "endOffset": 4038, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4069, "endOffset": 4107, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4132, "endOffset": 4167, "count": 0}], "isBlockCoverage": false}, {"functionName": "abor<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 4195, "endOffset": 4755, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4851, "endOffset": 5293, "count": 3}, {"startOffset": 5164, "endOffset": 5178, "count": 1}, {"startOffset": 5179, "endOffset": 5183, "count": 2}], "isBlockCoverage": true}, {"functionName": "addShellCommandToGeminiHistory", "ranges": [{"startOffset": 5304, "endOffset": 5758, "count": 3}, {"startOffset": 5443, "endOffset": 5509, "count": 0}], "isBlockCoverage": true}, {"functionName": "useShellCommandProcessor", "ranges": [{"startOffset": 5792, "endOffset": 9967, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5957, "endOffset": 9792, "count": 3}, {"startOffset": 6052, "endOffset": 6083, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6954, "endOffset": 9738, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7186, "endOffset": 7428, "count": 4}, {"startOffset": 7281, "endOffset": 7416, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7470, "endOffset": 9166, "count": 3}, {"startOffset": 7658, "endOffset": 7754, "count": 1}, {"startOffset": 7754, "endOffset": 7855, "count": 2}, {"startOffset": 7809, "endOffset": 7842, "count": 0}, {"startOffset": 7926, "endOffset": 8046, "count": 0}, {"startOffset": 8072, "endOffset": 8152, "count": 0}, {"startOffset": 8177, "endOffset": 8321, "count": 0}, {"startOffset": 8354, "endOffset": 8495, "count": 1}, {"startOffset": 8581, "endOffset": 8944, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9174, "endOffset": 9514, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9524, "endOffset": 9728, "count": 3}, {"startOffset": 9616, "endOffset": 9696, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1324", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/utils/formatters.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5378, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5378, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatMemoryUsage", "ranges": [{"startOffset": 515, "endOffset": 780, "count": 1}, {"startOffset": 649, "endOffset": 779, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatDuration", "ranges": [{"startOffset": 805, "endOffset": 1577, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}