{"version": 3, "file": "ToolStatsDisplay.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/ToolStatsDisplay.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EACL,cAAc,EACd,sBAAsB,EACtB,wBAAwB,EACxB,wBAAwB,EACxB,0BAA0B,GAC3B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAGhE,MAAM,mBAAmB,GAAG,EAAE,CAAC;AAC/B,MAAM,eAAe,GAAG,CAAC,CAAC;AAC1B,MAAM,sBAAsB,GAAG,EAAE,CAAC;AAClC,MAAM,sBAAsB,GAAG,EAAE,CAAC;AAElC,MAAM,OAAO,GAGR,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;IACvB,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,MAAM,YAAY,GAAG,cAAc,CAAC,WAAW,EAAE;QAC/C,KAAK,EAAE,sBAAsB;QAC7B,MAAM,EAAE,wBAAwB;KACjC,CAAC,CAAC;IAEH,OAAO,CACL,MAAC,GAAG,eACF,KAAC,GAAG,IAAC,KAAK,EAAE,mBAAmB,YAC7B,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,YAAG,IAAI,GAAQ,GACxC,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,eAAe,EAAE,cAAc,EAAC,UAAU,YACpD,KAAC,IAAI,cAAE,KAAK,CAAC,KAAK,GAAQ,GACtB,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,sBAAsB,EAAE,cAAc,EAAC,UAAU,YAC3D,MAAC,IAAI,IAAC,KAAK,EAAE,YAAY,aAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,GACvD,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,sBAAsB,EAAE,cAAc,EAAC,UAAU,YAC3D,KAAC,IAAI,cAAE,cAAc,CAAC,WAAW,CAAC,GAAQ,GACtC,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAa,GAAG,EAAE;IAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,eAAe,EAAE,CAAC;IACpC,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC;IAChC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,CACrD,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CACnC,CAAC;IAEF,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,CACL,KAAC,GAAG,IACF,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EACxB,QAAQ,EAAE,CAAC,EACX,QAAQ,EAAE,CAAC,YAEX,KAAC,IAAI,gEAAqD,GACtD,CACP,CAAC;IACJ,CAAC;IAED,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,CACvD,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;QACZ,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACpC,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACpC,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACpC,OAAO,GAAG,CAAC;IACb,CAAC,EACD,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CACpC,CAAC;IAEF,MAAM,aAAa,GACjB,cAAc,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;IACxE,MAAM,aAAa,GACjB,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACxE,MAAM,cAAc,GAAG,cAAc,CAAC,aAAa,EAAE;QACnD,KAAK,EAAE,wBAAwB;QAC/B,MAAM,EAAE,0BAA0B;KACnC,CAAC,CAAC;IAEH,OAAO,CACL,MAAC,GAAG,IACF,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EACxB,aAAa,EAAC,QAAQ,EACtB,QAAQ,EAAE,CAAC,EACX,QAAQ,EAAE,CAAC,EACX,KAAK,EAAE,EAAE,aAET,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,qCAE9B,EACP,KAAC,GAAG,IAAC,MAAM,EAAE,CAAC,GAAI,EAGlB,MAAC,GAAG,eACF,KAAC,GAAG,IAAC,KAAK,EAAE,mBAAmB,YAC7B,KAAC,IAAI,IAAC,IAAI,gCAAiB,GACvB,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,eAAe,EAAE,cAAc,EAAC,UAAU,YACpD,KAAC,IAAI,IAAC,IAAI,4BAAa,GACnB,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,sBAAsB,EAAE,cAAc,EAAC,UAAU,YAC3D,KAAC,IAAI,IAAC,IAAI,mCAAoB,GAC1B,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,sBAAsB,EAAE,cAAc,EAAC,UAAU,YAC3D,KAAC,IAAI,IAAC,IAAI,mCAAoB,GAC1B,IACF,EAGN,KAAC,GAAG,IACF,WAAW,EAAC,QAAQ,EACpB,YAAY,EAAE,IAAI,EAClB,SAAS,EAAE,KAAK,EAChB,UAAU,EAAE,KAAK,EACjB,WAAW,EAAE,KAAK,EAClB,KAAK,EAAC,MAAM,GACZ,EAGD,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAClC,KAAC,OAAO,IAAY,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAsB,IAA/C,IAAI,CAA+C,CAClE,CAAC,EAEF,KAAC,GAAG,IAAC,MAAM,EAAE,CAAC,GAAI,EAGlB,KAAC,IAAI,IAAC,IAAI,4CAA6B,EACvC,MAAC,GAAG,eACF,KAAC,GAAG,IACF,KAAK,EAAE,mBAAmB,GAAG,eAAe,GAAG,sBAAsB,YAErE,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,4CAAoC,GAC7D,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,sBAAsB,EAAE,cAAc,EAAC,UAAU,YAC3D,KAAC,IAAI,cAAE,aAAa,GAAQ,GACxB,IACF,EACN,MAAC,GAAG,eACF,KAAC,GAAG,IACF,KAAK,EAAE,mBAAmB,GAAG,eAAe,GAAG,sBAAsB,YAErE,KAAC,IAAI,oCAAoB,GACrB,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,sBAAsB,EAAE,cAAc,EAAC,UAAU,YAC3D,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,WAAW,YAAG,cAAc,CAAC,MAAM,GAAQ,GAC3D,IACF,EACN,MAAC,GAAG,eACF,KAAC,GAAG,IACF,KAAK,EAAE,mBAAmB,GAAG,eAAe,GAAG,sBAAsB,YAErE,KAAC,IAAI,oCAAoB,GACrB,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,sBAAsB,EAAE,cAAc,EAAC,UAAU,YAC3D,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,YAAG,cAAc,CAAC,MAAM,GAAQ,GACzD,IACF,EACN,MAAC,GAAG,eACF,KAAC,GAAG,IACF,KAAK,EAAE,mBAAmB,GAAG,eAAe,GAAG,sBAAsB,YAErE,KAAC,IAAI,oCAAoB,GACrB,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,sBAAsB,EAAE,cAAc,EAAC,UAAU,YAC3D,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,YAAY,YAAG,cAAc,CAAC,MAAM,GAAQ,GAC5D,IACF,EAGN,KAAC,GAAG,IACF,WAAW,EAAC,QAAQ,EACpB,YAAY,EAAE,IAAI,EAClB,SAAS,EAAE,KAAK,EAChB,UAAU,EAAE,KAAK,EACjB,WAAW,EAAE,KAAK,EAClB,KAAK,EAAC,MAAM,GACZ,EAEF,MAAC,GAAG,eACF,KAAC,GAAG,IACF,KAAK,EAAE,mBAAmB,GAAG,eAAe,GAAG,sBAAsB,YAErE,KAAC,IAAI,2CAAgC,GACjC,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,sBAAsB,EAAE,cAAc,EAAC,UAAU,YAC3D,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,YAC7D,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GACrD,GACH,IACF,IACF,CACP,CAAC;AACJ,CAAC,CAAC"}