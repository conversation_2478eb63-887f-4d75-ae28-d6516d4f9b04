{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/slashCommandProcessor.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 140467, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 140467, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 470, "endOffset": 547, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 526, "endOffset": 543, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 596, "endOffset": 1511, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 699, "count": 0}], "isBlockCoverage": false}, {"functionName": "get env", "ranges": [{"startOffset": 706, "endOffset": 749, "count": 19}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 919, "endOffset": 1060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1184, "endOffset": 1201, "count": 0}], "isBlockCoverage": false}, {"functionName": "get env", "ranges": [{"startOffset": 1206, "endOffset": 1245, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1378, "endOffset": 1507, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1564, "endOffset": 1701, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1760, "endOffset": 1830, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCliVersion", "ranges": [{"startOffset": 1786, "endOffset": 1827, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1896, "endOffset": 1956, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2085, "endOffset": 2231, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2192, "endOffset": 2227, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2272, "endOffset": 2324, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2376, "endOffset": 2588, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3650, "endOffset": 3680, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3756, "endOffset": 48149, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4206, "endOffset": 6451, "count": 30}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4354, "endOffset": 4565, "count": 25}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5322, "endOffset": 5333, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGeminiClient", "ranges": [{"startOffset": 5359, "endOffset": 5381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5429, "endOffset": 5449, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5496, "endOffset": 5514, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5567, "endOffset": 5584, "count": 60}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5646, "endOffset": 5656, "count": 61}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5708, "endOffset": 5720, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5771, "endOffset": 5794, "count": 30}], "isBlockCoverage": true}, {"functionName": "getProcessorHook", "ranges": [{"startOffset": 6481, "endOffset": 7163, "count": 29}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6653, "endOffset": 7152, "count": 59}], "isBlockCoverage": true}, {"functionName": "getProcessor", "ranges": [{"startOffset": 7188, "endOffset": 7275, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7332, "endOffset": 9434, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7416, "endOffset": 8262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7810, "endOffset": 7869, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8363, "endOffset": 8846, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8462, "endOffset": 8527, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8946, "endOffset": 9428, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9045, "endOffset": 9110, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9492, "endOffset": 12117, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9606, "endOffset": 11318, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10098, "endOffset": 10618, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10660, "endOffset": 10740, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11406, "endOffset": 12111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11757, "endOffset": 11837, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12175, "endOffset": 12665, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12272, "endOffset": 12659, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12404, "endOffset": 12486, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12729, "endOffset": 19121, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12781, "endOffset": 12970, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13014, "endOffset": 13075, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13161, "endOffset": 14127, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 13319, "endOffset": 13343, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13518, "endOffset": 13546, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13646, "endOffset": 13795, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13740, "endOffset": 13764, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13863, "endOffset": 13958, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14237, "endOffset": 15322, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 14512, "endOffset": 14536, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14711, "endOffset": 14739, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14839, "endOffset": 14988, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14933, "endOffset": 14957, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15429, "endOffset": 16690, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 15707, "endOffset": 15731, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15906, "endOffset": 15934, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16034, "endOffset": 16183, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16128, "endOffset": 16152, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16796, "endOffset": 17837, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 17030, "endOffset": 17054, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17229, "endOffset": 17257, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17357, "endOffset": 17506, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17451, "endOffset": 17475, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17929, "endOffset": 19115, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 18156, "endOffset": 18183, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18358, "endOffset": 18386, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18486, "endOffset": 18637, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18580, "endOffset": 18606, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18672, "endOffset": 18753, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19177, "endOffset": 23141, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19263, "endOffset": 19417, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19460, "endOffset": 19554, "count": 2}], "isBlockCoverage": true}, {"functionName": "getExpectedUrl", "ranges": [{"startOffset": 19584, "endOffset": 20642, "count": 1}, {"startOffset": 19892, "endOffset": 20022, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20747, "endOffset": 21621, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21255, "endOffset": 21352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21723, "endOffset": 23135, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22093, "endOffset": 22109, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22769, "endOffset": 22866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23209, "endOffset": 24551, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23258, "endOffset": 23319, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23362, "endOffset": 23423, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23562, "endOffset": 24540, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23809, "endOffset": 23871, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24363, "endOffset": 24447, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24610, "endOffset": 25360, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24724, "endOffset": 25354, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24856, "endOffset": 24946, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25418, "endOffset": 30447, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25517, "endOffset": 26265, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25783, "endOffset": 25864, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26358, "endOffset": 27188, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26706, "endOffset": 26787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 27290, "endOffset": 28255, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 27889, "endOffset": 27970, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28358, "endOffset": 29170, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28859, "endOffset": 28940, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29267, "endOffset": 30441, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29902, "endOffset": 29988, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30503, "endOffset": 44190, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30602, "endOffset": 31356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30868, "endOffset": 30947, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 31479, "endOffset": 32560, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 31941, "endOffset": 32020, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 32692, "endOffset": 33795, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 33115, "endOffset": 33194, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 33911, "endOffset": 36855, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 34171, "endOffset": 34433, "count": 6}, {"startOffset": 34227, "endOffset": 34276, "count": 2}, {"startOffset": 34276, "endOffset": 34315, "count": 4}, {"startOffset": 34315, "endOffset": 34432, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 34905, "endOffset": 35138, "count": 3}, {"startOffset": 34961, "endOffset": 34985, "count": 1}, {"startOffset": 34985, "endOffset": 35024, "count": 2}, {"startOffset": 35024, "endOffset": 35111, "count": 1}, {"startOffset": 35111, "endOffset": 35137, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35533, "endOffset": 35612, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36961, "endOffset": 39378, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 37218, "endOffset": 37392, "count": 2}, {"startOffset": 37323, "endOffset": 37391, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 38167, "endOffset": 38246, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 39460, "endOffset": 41857, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 39682, "endOffset": 39947, "count": 4}, {"startOffset": 39738, "endOffset": 39878, "count": 2}, {"startOffset": 39878, "endOffset": 39946, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40283, "endOffset": 40453, "count": 2}, {"startOffset": 40339, "endOffset": 40426, "count": 1}, {"startOffset": 40426, "endOffset": 40452, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40848, "endOffset": 40927, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 41954, "endOffset": 44184, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 42176, "endOffset": 42439, "count": 4}, {"startOffset": 42232, "endOffset": 42370, "count": 2}, {"startOffset": 42370, "endOffset": 42438, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 42802, "endOffset": 42972, "count": 2}, {"startOffset": 42858, "endOffset": 42945, "count": 1}, {"startOffset": 42945, "endOffset": 42971, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 43367, "endOffset": 43446, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 44245, "endOffset": 46985, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 44334, "endOffset": 46979, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 44591, "endOffset": 44765, "count": 2}, {"startOffset": 44696, "endOffset": 44764, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 45784, "endOffset": 45870, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 47046, "endOffset": 48145, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 47124, "endOffset": 48139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 47324, "endOffset": 47406, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 47441, "endOffset": 47487, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1320", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/slashCommandProcessor.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 126096, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 126096, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 61}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "useSlashCommandProcessor", "ranges": [{"startOffset": 2371, "endOffset": 41127, "count": 61}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2812, "endOffset": 2955, "count": 30}, {"startOffset": 2855, "endOffset": 2876, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3019, "endOffset": 3122, "count": 30}, {"startOffset": 3097, "endOffset": 3102, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3312, "endOffset": 3476, "count": 30}, {"startOffset": 3393, "endOffset": 3453, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3572, "endOffset": 5083, "count": 23}, {"startOffset": 3685, "endOffset": 4026, "count": 2}, {"startOffset": 4026, "endOffset": 5012, "count": 21}, {"startOffset": 4094, "endOffset": 4207, "count": 1}, {"startOffset": 4207, "endOffset": 5012, "count": 20}, {"startOffset": 4281, "endOffset": 4362, "count": 1}, {"startOffset": 4362, "endOffset": 5012, "count": 19}, {"startOffset": 4435, "endOffset": 4515, "count": 1}, {"startOffset": 4515, "endOffset": 5012, "count": 18}, {"startOffset": 4582, "endOffset": 4694, "count": 0}, {"startOffset": 4768, "endOffset": 4893, "count": 1}, {"startOffset": 4893, "endOffset": 5012, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5168, "endOffset": 5528, "count": 30}], "isBlockCoverage": true}, {"functionName": "clear", "ranges": [{"startOffset": 5322, "endOffset": 5417, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5764, "endOffset": 5813, "count": 30}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5858, "endOffset": 6011, "count": 30}], "isBlockCoverage": true}, {"functionName": "load", "ranges": [{"startOffset": 5883, "endOffset": 5994, "count": 30}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6094, "endOffset": 6490, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6562, "endOffset": 35166, "count": 61}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 6792, "endOffset": 7616, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 7714, "endOffset": 7792, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 7895, "endOffset": 7949, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 8062, "endOffset": 8118, "count": 1}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 8228, "endOffset": 8285, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 8441, "endOffset": 9352, "count": 3}, {"startOffset": 8518, "endOffset": 8711, "count": 1}, {"startOffset": 8711, "endOffset": 8937, "count": 2}, {"startOffset": 8745, "endOffset": 8937, "count": 1}, {"startOffset": 8937, "endOffset": 9351, "count": 1}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 9469, "endOffset": 16187, "count": 8}, {"startOffset": 9645, "endOffset": 9698, "count": 0}, {"startOffset": 9770, "endOffset": 9824, "count": 0}, {"startOffset": 9880, "endOffset": 9933, "count": 0}, {"startOffset": 9993, "endOffset": 10047, "count": 0}, {"startOffset": 10125, "endOffset": 10146, "count": 7}, {"startOffset": 10148, "endOffset": 10195, "count": 1}, {"startOffset": 10290, "endOffset": 10537, "count": 1}, {"startOffset": 10537, "endOffset": 10589, "count": 7}, {"startOffset": 10592, "endOffset": 10597, "count": 0}, {"startOffset": 10694, "endOffset": 11581, "count": 2}, {"startOffset": 10821, "endOffset": 10884, "count": 1}, {"startOffset": 10886, "endOffset": 11548, "count": 1}, {"startOffset": 11581, "endOffset": 11974, "count": 5}, {"startOffset": 11974, "endOffset": 12005, "count": 4}, {"startOffset": 12007, "endOffset": 12263, "count": 1}, {"startOffset": 12263, "endOffset": 12365, "count": 5}, {"startOffset": 12365, "endOffset": 15969, "count": 9}, {"startOffset": 12642, "endOffset": 12796, "count": 6}, {"startOffset": 12811, "endOffset": 13004, "count": 1}, {"startOffset": 13019, "endOffset": 13075, "count": 2}, {"startOffset": 13090, "endOffset": 13206, "count": 2}, {"startOffset": 13438, "endOffset": 13512, "count": 6}, {"startOffset": 13512, "endOffset": 13745, "count": 3}, {"startOffset": 13583, "endOffset": 13658, "count": 1}, {"startOffset": 13658, "endOffset": 13745, "count": 2}, {"startOffset": 13784, "endOffset": 13800, "count": 7}, {"startOffset": 13802, "endOffset": 13824, "count": 2}, {"startOffset": 13826, "endOffset": 14283, "count": 2}, {"startOffset": 14213, "endOffset": 14269, "count": 0}, {"startOffset": 14283, "endOffset": 14335, "count": 7}, {"startOffset": 14411, "endOffset": 15855, "count": 8}, {"startOffset": 15855, "endOffset": 15927, "count": 1}, {"startOffset": 15969, "endOffset": 16186, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11651, "endOffset": 11764, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14447, "endOffset": 15839, "count": 11}, {"startOffset": 14500, "endOffset": 14516, "count": 7}, {"startOffset": 14518, "endOffset": 14537, "count": 4}, {"startOffset": 14539, "endOffset": 15108, "count": 4}, {"startOffset": 15026, "endOffset": 15090, "count": 0}, {"startOffset": 15108, "endOffset": 15198, "count": 7}, {"startOffset": 15235, "endOffset": 15823, "count": 2}, {"startOffset": 15679, "endOffset": 15785, "count": 12}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 16296, "endOffset": 17081, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 17194, "endOffset": 19340, "count": 5}, {"startOffset": 17335, "endOffset": 17368, "count": 4}, {"startOffset": 17370, "endOffset": 17423, "count": 1}, {"startOffset": 17423, "endOffset": 17772, "count": 4}, {"startOffset": 17495, "endOffset": 17549, "count": 0}, {"startOffset": 17605, "endOffset": 17658, "count": 0}, {"startOffset": 17718, "endOffset": 17772, "count": 0}, {"startOffset": 17874, "endOffset": 17887, "count": 4}, {"startOffset": 17913, "endOffset": 18152, "count": 2}, {"startOffset": 18152, "endOffset": 18330, "count": 3}, {"startOffset": 18330, "endOffset": 19027, "count": 2}, {"startOffset": 19027, "endOffset": 19095, "count": 1}, {"startOffset": 19095, "endOffset": 19339, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18197, "endOffset": 18230, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18364, "endOffset": 19013, "count": 4}, {"startOffset": 18414, "endOffset": 18433, "count": 2}, {"startOffset": 18435, "endOffset": 18999, "count": 2}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 19397, "endOffset": 19475, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 19574, "endOffset": 20743, "count": 2}, {"startOffset": 19850, "endOffset": 20126, "count": 1}, {"startOffset": 20098, "endOffset": 20110, "count": 0}, {"startOffset": 20178, "endOffset": 20190, "count": 0}, {"startOffset": 20339, "endOffset": 20344, "count": 1}, {"startOffset": 20430, "endOffset": 20435, "count": 0}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 20842, "endOffset": 23404, "count": 2}, {"startOffset": 20930, "endOffset": 20935, "count": 0}, {"startOffset": 21340, "endOffset": 21650, "count": 1}, {"startOffset": 21622, "endOffset": 21634, "count": 0}, {"startOffset": 21702, "endOffset": 21714, "count": 0}, {"startOffset": 22417, "endOffset": 22430, "count": 1}, {"startOffset": 22432, "endOffset": 22496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22906, "endOffset": 23390, "count": 2}, {"startOffset": 23021, "endOffset": 23378, "count": 0}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 23551, "endOffset": 28067, "count": 0}], "isBlockCoverage": false}, {"functionName": "completion", "ranges": [{"startOffset": 28089, "endOffset": 28154, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 28272, "endOffset": 28951, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28864, "endOffset": 28934, "count": 2}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 29120, "endOffset": 30900, "count": 1}, {"startOffset": 29224, "endOffset": 29496, "count": 0}, {"startOffset": 30307, "endOffset": 30551, "count": 0}, {"startOffset": 30564, "endOffset": 30846, "count": 0}], "isBlockCoverage": true}, {"functionName": "completion", "ranges": [{"startOffset": 31180, "endOffset": 31666, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 31684, "endOffset": 35123, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 35622, "endOffset": 39952, "count": 30}, {"startOffset": 35684, "endOffset": 35715, "count": 0}, {"startOffset": 35791, "endOffset": 35818, "count": 0}, {"startOffset": 35820, "endOffset": 35851, "count": 0}, {"startOffset": 35930, "endOffset": 35952, "count": 29}, {"startOffset": 35954, "endOffset": 36097, "count": 28}, {"startOffset": 36491, "endOffset": 36716, "count": 5}, {"startOffset": 36599, "endOffset": 36668, "count": 1}, {"startOffset": 36668, "endOffset": 36706, "count": 4}, {"startOffset": 36716, "endOffset": 36750, "count": 25}, {"startOffset": 36788, "endOffset": 38738, "count": 5}, {"startOffset": 36882, "endOffset": 38267, "count": 4}, {"startOffset": 36984, "endOffset": 38218, "count": 3}, {"startOffset": 37035, "endOffset": 37221, "count": 1}, {"startOffset": 37236, "endOffset": 37591, "count": 1}, {"startOffset": 37354, "endOffset": 37395, "count": 0}, {"startOffset": 37606, "endOffset": 38028, "count": 1}, {"startOffset": 37798, "endOffset": 38010, "count": 0}, {"startOffset": 38043, "endOffset": 38192, "count": 0}, {"startOffset": 38218, "endOffset": 38267, "count": 1}, {"startOffset": 38267, "endOffset": 38730, "count": 1}, {"startOffset": 38738, "endOffset": 38902, "count": 25}, {"startOffset": 38902, "endOffset": 39733, "count": 228}, {"startOffset": 38941, "endOffset": 38971, "count": 205}, {"startOffset": 38973, "endOffset": 39725, "count": 24}, {"startOffset": 39135, "endOffset": 39141, "count": 0}, {"startOffset": 39154, "endOffset": 39333, "count": 0}, {"startOffset": 39361, "endOffset": 39367, "count": 0}, {"startOffset": 39383, "endOffset": 39676, "count": 0}, {"startOffset": 39733, "endOffset": 39951, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36200, "endOffset": 36208, "count": 44}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36403, "endOffset": 36453, "count": 5}, {"startOffset": 36430, "endOffset": 36453, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 38442, "endOffset": 38491, "count": 1}, {"startOffset": 38484, "endOffset": 38489, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40138, "endOffset": 40979, "count": 61}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40206, "endOffset": 40737, "count": 976}, {"startOffset": 40662, "endOffset": 40719, "count": 122}, {"startOffset": 40720, "endOffset": 40728, "count": 854}], "isBlockCoverage": true}, {"functionName": "action", "ranges": [{"startOffset": 40350, "endOffset": 40619, "count": 0}], "isBlockCoverage": false}, {"functionName": "completion", "ranges": [{"startOffset": 40664, "endOffset": 40719, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 40794, "endOffset": 40807, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40881, "endOffset": 40916, "count": 976}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1323", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/useStateAndRef.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3287, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3287, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 61}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "useStateAndRef", "ranges": [{"startOffset": 563, "endOffset": 1126, "count": 61}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 801, "endOffset": 1077, "count": 2}, {"startOffset": 899, "endOffset": 960, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1324", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1814, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1814, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 333, "count": 1}, {"startOffset": 323, "endOffset": 331, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 390, "endOffset": 472, "count": 1}, {"startOffset": 462, "endOffset": 470, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 533, "endOffset": 619, "count": 1}, {"startOffset": 609, "endOffset": 617, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1325", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11673, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11673, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 312, "count": 1}, {"startOffset": 302, "endOffset": 310, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1326", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/config/config.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 50707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 50707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 390, "count": 1}, {"startOffset": 380, "endOffset": 388, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 467, "count": 1}, {"startOffset": 457, "endOffset": 465, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 524, "endOffset": 607, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4742, "endOffset": 4889, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4951, "endOffset": 5832, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5847, "endOffset": 17225, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1327", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/contentGenerator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 408, "count": 1}, {"startOffset": 398, "endOffset": 406, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 517, "count": 1}, {"startOffset": 507, "endOffset": 515, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1341, "endOffset": 1555, "count": 1}], "isBlockCoverage": true}, {"functionName": "createContentGeneratorConfig", "ranges": [{"startOffset": 1587, "endOffset": 3116, "count": 0}], "isBlockCoverage": false}, {"functionName": "createContentGenerator", "ranges": [{"startOffset": 3118, "endOffset": 4086, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1535", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/codeAssist.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3709, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3709, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 269, "endOffset": 335, "count": 1}, {"startOffset": 325, "endOffset": 333, "count": 0}], "isBlockCoverage": true}, {"functionName": "createCodeAssistContentGenerator", "ranges": [{"startOffset": 1115, "endOffset": 1677, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1536", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/oauth2.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 396, "count": 1}, {"startOffset": 386, "endOffset": 394, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 451, "endOffset": 509, "count": 1}, {"startOffset": 499, "endOffset": 507, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 565, "endOffset": 624, "count": 1}, {"startOffset": 614, "endOffset": 622, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 676, "endOffset": 731, "count": 1}, {"startOffset": 721, "endOffset": 729, "count": 0}], "isBlockCoverage": true}, {"functionName": "getOauthClient", "ranges": [{"startOffset": 3741, "endOffset": 6279, "count": 0}], "isBlockCoverage": false}, {"functionName": "authWithWeb", "ranges": [{"startOffset": 6281, "endOffset": 9224, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailablePort", "ranges": [{"startOffset": 9225, "endOffset": 9840, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCachedCredentials", "ranges": [{"startOffset": 9842, "endOffset": 10480, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheCredentials", "ranges": [{"startOffset": 10481, "endOffset": 10774, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedCredentialPath", "ranges": [{"startOffset": 10775, "endOffset": 10926, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGoogleAccountIdCachePath", "ranges": [{"startOffset": 10927, "endOffset": 11089, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheGoogleAccountId", "ranges": [{"startOffset": 11090, "endOffset": 11348, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedGoogleAccountId", "ranges": [{"startOffset": 11349, "endOffset": 11715, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearCachedCredentialFile", "ranges": [{"startOffset": 11717, "endOffset": 12029, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRawGoogleAccountId", "ranges": [{"startOffset": 12239, "endOffset": 13379, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1539", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/errors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6685, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6685, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 1}, {"startOffset": 471, "endOffset": 479, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 1}, {"startOffset": 570, "endOffset": 578, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 1}, {"startOffset": 665, "endOffset": 673, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 1}, {"startOffset": 760, "endOffset": 768, "count": 0}], "isBlockCoverage": true}, {"functionName": "isNodeError", "ranges": [{"startOffset": 1030, "endOffset": 1115, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1117, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1459, "endOffset": 2277, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 2279, "endOffset": 2537, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1547", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8065, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8065, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "ProjectIdRequiredError", "ranges": [{"startOffset": 883, "endOffset": 1041, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupUser", "ranges": [{"startOffset": 1147, "endOffset": 2390, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOnboardTier", "ranges": [{"startOffset": 2392, "endOffset": 2777, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1548", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 1}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 404, "count": 1}, {"startOffset": 394, "endOffset": 402, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 456, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 837, "endOffset": 1482, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2147, "endOffset": 2377, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1549", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/server.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23446, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23446, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 422, "count": 1}, {"startOffset": 412, "endOffset": 420, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 1}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1303, "endOffset": 7234, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1554", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/converter.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 650, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCountTokenRequest", "ranges": [{"startOffset": 926, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromCountTokenResponse", "ranges": [{"startOffset": 1105, "endOffset": 1203, "count": 0}], "isBlockCoverage": false}, {"functionName": "toGenerateContentRequest", "ranges": [{"startOffset": 1205, "endOffset": 1395, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromGenerateContentResponse", "ranges": [{"startOffset": 1397, "endOffset": 1771, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerateContentRequest", "ranges": [{"startOffset": 1773, "endOffset": 2281, "count": 0}], "isBlockCoverage": false}, {"functionName": "toContents", "ranges": [{"startOffset": 2282, "endOffset": 2515, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2516, "endOffset": 2636, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2637, "endOffset": 3170, "count": 0}], "isBlockCoverage": false}, {"functionName": "toParts", "ranges": [{"startOffset": 3171, "endOffset": 3228, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON>", "ranges": [{"startOffset": 3229, "endOffset": 3370, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerationConfig", "ranges": [{"startOffset": 3371, "endOffset": 4380, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1555", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/config/models.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1684, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1684, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 368, "endOffset": 428, "count": 2}, {"startOffset": 418, "endOffset": 426, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 489, "endOffset": 553, "count": 1}, {"startOffset": 543, "endOffset": 551, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1556", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/modelCheck.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7710, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7710, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEffectiveModel", "ranges": [{"startOffset": 1018, "endOffset": 2814, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1557", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/tool-registry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41121, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41121, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 437, "endOffset": 489, "count": 1}, {"startOffset": 479, "endOffset": 487, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1900, "endOffset": 5313, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5334, "endOffset": 12112, "count": 0}], "isBlockCoverage": true}, {"functionName": "sanitizeParameters", "ranges": [{"startOffset": 12713, "endOffset": 12796, "count": 0}], "isBlockCoverage": false}, {"functionName": "_sanitizeParameters", "ranges": [{"startOffset": 12994, "endOffset": 14077, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1558", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/tools.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15463, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15463, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 14}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 1}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 571, "endOffset": 3280, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3312, "endOffset": 3750, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1559", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/mcp-client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 1}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 414, "count": 1}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 462, "endOffset": 513, "count": 1}, {"startOffset": 503, "endOffset": 511, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 570, "endOffset": 630, "count": 1}, {"startOffset": 620, "endOffset": 628, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 690, "endOffset": 753, "count": 1}, {"startOffset": 743, "endOffset": 751, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 802, "endOffset": 854, "count": 1}, {"startOffset": 844, "endOffset": 852, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 908, "endOffset": 965, "count": 1}, {"startOffset": 955, "endOffset": 963, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1070, "count": 1}, {"startOffset": 1060, "endOffset": 1068, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1117, "endOffset": 1167, "count": 1}, {"startOffset": 1157, "endOffset": 1165, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2902, "endOffset": 3239, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3370, "endOffset": 3707, "count": 1}], "isBlockCoverage": true}, {"functionName": "addMCPStatusChangeListener", "ranges": [{"startOffset": 4074, "endOffset": 4165, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeMCPStatusChangeListener", "ranges": [{"startOffset": 4226, "endOffset": 4415, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateMCPServerStatus", "ranges": [{"startOffset": 4463, "endOffset": 4696, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPServerStatus", "ranges": [{"startOffset": 4748, "endOffset": 4879, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllMCPServerStatuses", "ranges": [{"startOffset": 4920, "endOffset": 5005, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPDiscoveryState", "ranges": [{"startOffset": 5054, "endOffset": 5119, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverMcpTools", "ranges": [{"startOffset": 5121, "endOffset": 6262, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectAndDiscover", "ranges": [{"startOffset": 6794, "endOffset": 15128, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1653", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/mcp-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16421, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16421, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 673, "endOffset": 3111, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 673, "endOffset": 3111, "count": 0}], "isBlockCoverage": true}, {"functionName": "getStringifiedResultForDisplay", "ranges": [{"startOffset": 4240, "endOffset": 5653, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1654", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/ls.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29509, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 29509, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 1}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1434, "endOffset": 10361, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1434, "endOffset": 10361, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1655", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/schemaValidator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8007, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8007, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "validate", "ranges": [{"startOffset": 854, "endOffset": 1336, "count": 0}], "isBlockCoverage": false}, {"functionName": "toObjectSchema", "ranges": [{"startOffset": 1599, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1739", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/paths.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 2}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 334, "endOffset": 380, "count": 1}, {"startOffset": 370, "endOffset": 378, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 422, "endOffset": 467, "count": 1}, {"startOffset": 457, "endOffset": 465, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 510, "endOffset": 556, "count": 1}, {"startOffset": 546, "endOffset": 554, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 597, "endOffset": 641, "count": 1}, {"startOffset": 631, "endOffset": 639, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 684, "endOffset": 730, "count": 1}, {"startOffset": 720, "endOffset": 728, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 775, "endOffset": 823, "count": 1}, {"startOffset": 813, "endOffset": 821, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 871, "endOffset": 922, "count": 1}, {"startOffset": 912, "endOffset": 920, "count": 0}], "isBlockCoverage": true}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1548, "endOffset": 1743, "count": 0}], "isBlockCoverage": false}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1911, "endOffset": 4322, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 4760, "endOffset": 5201, "count": 0}], "isBlockCoverage": false}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 5245, "endOffset": 5608, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 5654, "endOffset": 5731, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 5931, "endOffset": 6060, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 6264, "endOffset": 6473, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1740", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/read-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16973, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16973, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1752, "endOffset": 6265, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1752, "endOffset": 6265, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1741", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/fileUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 36964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 495, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 538, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 629, "endOffset": 677, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 790, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSpecificMimeType", "ranges": [{"startOffset": 1719, "endOffset": 1906, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 2149, "endOffset": 2941, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinaryFile", "ranges": [{"startOffset": 3107, "endOffset": 4503, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectFileType", "ranges": [{"startOffset": 4677, "endOffset": 6461, "count": 0}], "isBlockCoverage": false}, {"functionName": "processSingleFileContent", "ranges": [{"startOffset": 6861, "endOffset": 13076, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1748", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/metrics.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18804, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18804, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 426, "endOffset": 477, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 750, "endOffset": 808, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 860, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 971, "endOffset": 1030, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1681, "endOffset": 1825, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2070, "endOffset": 2175, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMeter", "ranges": [{"startOffset": 2176, "endOffset": 2347, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeMetrics", "ranges": [{"startOffset": 2349, "endOffset": 4246, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordToolCallMetrics", "ranges": [{"startOffset": 4248, "endOffset": 4762, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordTokenUsageMetrics", "ranges": [{"startOffset": 4764, "endOffset": 5019, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiResponseMetrics", "ranges": [{"startOffset": 5021, "endOffset": 5537, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiErrorMetrics", "ranges": [{"startOffset": 5539, "endOffset": 6083, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordFileOperationMetric", "ranges": [{"startOffset": 6085, "endOffset": 6588, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1844", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/constants.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 440, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 537, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 634, "endOffset": 683, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 784, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 881, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 934, "endOffset": 990, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1045, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1273, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1382, "endOffset": 1434, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1597, "endOffset": 1658, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1845", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/grep.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 63513, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 63513, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2499, "endOffset": 22317, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2499, "endOffset": 22317, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1865", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/gitUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6303, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6303, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 844, "endOffset": 1653, "count": 0}], "isBlockCoverage": false}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1842, "endOffset": 2439, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1866", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/glob.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 31867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 31867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 382, "count": 1}, {"startOffset": 372, "endOffset": 380, "count": 0}], "isBlockCoverage": true}, {"functionName": "sortFileEntries", "ranges": [{"startOffset": 1799, "endOffset": 2492, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2624, "endOffset": 11131, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2624, "endOffset": 11131, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1867", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/edit.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 55752, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 55752, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2389, "endOffset": 19604, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2389, "endOffset": 19604, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1869", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/editCorrector.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 81034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 81034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 578, "endOffset": 628, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 683, "endOffset": 741, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 793, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 905, "endOffset": 965, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1012, "endOffset": 1062, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1127, "endOffset": 1195, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTimestampFromFunctionId", "ranges": [{"startOffset": 3627, "endOffset": 3888, "count": 0}], "isBlockCoverage": false}, {"functionName": "findLastEditTimestamp", "ranges": [{"startOffset": 4254, "endOffset": 6803, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectEdit", "ranges": [{"startOffset": 7332, "endOffset": 13596, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectFileContent", "ranges": [{"startOffset": 13598, "endOffset": 14191, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctOldStringMismatch", "ranges": [{"startOffset": 14681, "endOffset": 17041, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewString", "ranges": [{"startOffset": 17647, "endOffset": 20453, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewStringEscaping", "ranges": [{"startOffset": 20936, "endOffset": 23240, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctStringEscaping", "ranges": [{"startOffset": 23672, "endOffset": 25581, "count": 0}], "isBlockCoverage": false}, {"functionName": "trimPairIfPossible", "ranges": [{"startOffset": 25583, "endOffset": 26243, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapeStringForGeminiBug", "ranges": [{"startOffset": 26321, "endOffset": 28630, "count": 0}], "isBlockCoverage": false}, {"functionName": "countOccurrences", "ranges": [{"startOffset": 28689, "endOffset": 28998, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetEditCorrectorCaches_TEST_ONLY", "ranges": [{"startOffset": 29000, "endOffset": 29122, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1870", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/write-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 42741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 42741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 1}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2724, "endOffset": 15185, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2724, "endOffset": 15185, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1871", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/diffOptions.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1271, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1271, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1872", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/read-many-files.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 52914, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 52914, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 3380, "endOffset": 18642, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3380, "endOffset": 18642, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1873", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/memoryTool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26084, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26084, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 418, "count": 1}, {"startOffset": 408, "endOffset": 416, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 470, "endOffset": 525, "count": 1}, {"startOffset": 515, "endOffset": 523, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 575, "endOffset": 628, "count": 1}, {"startOffset": 618, "endOffset": 626, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 685, "endOffset": 745, "count": 2}, {"startOffset": 735, "endOffset": 743, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 799, "endOffset": 856, "count": 1}, {"startOffset": 846, "endOffset": 854, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 897, "endOffset": 941, "count": 1}, {"startOffset": 931, "endOffset": 939, "count": 0}], "isBlockCoverage": true}, {"functionName": "setGeminiMdFilename", "ranges": [{"startOffset": 3915, "endOffset": 4247, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentGeminiMdFilename", "ranges": [{"startOffset": 4249, "endOffset": 4426, "count": 1}, {"startOffset": 4337, "endOffset": 4387, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAllGeminiMdFilenames", "ranges": [{"startOffset": 4428, "endOffset": 4601, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGlobalMemoryFilePath", "ranges": [{"startOffset": 4603, "endOffset": 4740, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureNewlineSeparation", "ranges": [{"startOffset": 4812, "endOffset": 5133, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 5217, "endOffset": 9360, "count": 1}], "isBlockCoverage": true}, {"functionName": "MemoryTool", "ranges": [{"startOffset": 5268, "endOffset": 5392, "count": 0}], "isBlockCoverage": false}, {"functionName": "performAddMemoryEntry", "ranges": [{"startOffset": 5404, "endOffset": 7822, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7827, "endOffset": 9358, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1874", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/LruCache.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3889, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3889, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 2}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 393, "endOffset": 1181, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 423, "endOffset": 515, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 520, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 766, "endOffset": 1131, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1136, "endOffset": 1179, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1875", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/messageInspectors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2484, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2484, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 352, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFunctionResponse", "ranges": [{"startOffset": 491, "endOffset": 662, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFunctionCall", "ranges": [{"startOffset": 664, "endOffset": 828, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1876", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/shell.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 58783, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 58783, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2048, "endOffset": 20771, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2048, "endOffset": 20771, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1879", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/web-fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37380, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37380, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "extractUrls", "ranges": [{"startOffset": 1920, "endOffset": 2034, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2173, "endOffset": 12420, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2173, "endOffset": 12420, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1880", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/generateContentResponseUtilities.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11697, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11697, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 567, "endOffset": 626, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 679, "endOffset": 735, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 797, "endOffset": 862, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 914, "endOffset": 969, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1030, "endOffset": 1094, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseText", "ranges": [{"startOffset": 1185, "endOffset": 1556, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseTextFromParts", "ranges": [{"startOffset": 1558, "endOffset": 1875, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCalls", "ranges": [{"startOffset": 1877, "endOffset": 2226, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromParts", "ranges": [{"startOffset": 2228, "endOffset": 2523, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsAsJson", "ranges": [{"startOffset": 2525, "endOffset": 2734, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromPartsAsJson", "ranges": [{"startOffset": 2736, "endOffset": 2957, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponse", "ranges": [{"startOffset": 2959, "endOffset": 3374, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponseFromParts", "ranges": [{"startOffset": 3376, "endOffset": 3809, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1881", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5599, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5599, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 333, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 425, "endOffset": 475, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1111, "endOffset": 1247, "count": 0}], "isBlockCoverage": true}, {"functionName": "isPrivateIp", "ranges": [{"startOffset": 1249, "endOffset": 1459, "count": 0}], "isBlockCoverage": false}, {"functionName": "fetchWithTimeout", "ranges": [{"startOffset": 1461, "endOffset": 2087, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1914", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/web-search.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 1}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1425, "endOffset": 6487, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1425, "endOffset": 6487, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1915", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 62593, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 62593, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 1}, {"startOffset": 305, "endOffset": 313, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 358, "endOffset": 404, "count": 1}, {"startOffset": 394, "endOffset": 402, "count": 0}], "isBlockCoverage": true}, {"functionName": "isThinkingSupported", "ranges": [{"startOffset": 2827, "endOffset": 2945, "count": 0}], "isBlockCoverage": false}, {"functionName": "findIndexAfterFraction", "ranges": [{"startOffset": 3086, "endOffset": 3730, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3751, "endOffset": 21261, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1916", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/getFolderStructure.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38265, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38265, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "readFullStructure", "ranges": [{"startOffset": 962, "endOffset": 6781, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatStructure", "ranges": [{"startOffset": 7049, "endOffset": 9323, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFolderStructure", "ranges": [{"startOffset": 9744, "endOffset": 12221, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1917", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/turn.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20810, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20810, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 374, "count": 1}, {"startOffset": 364, "endOffset": 372, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1062, "endOffset": 1534, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1658, "endOffset": 6066, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1918", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/errorReporting.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13055, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13055, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "reportError", "ranges": [{"startOffset": 1188, "endOffset": 4499, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1919", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/prompts.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 68477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 68477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 414, "count": 1}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "getCoreSystemPrompt", "ranges": [{"startOffset": 2579, "endOffset": 25919, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCompressionPrompt", "ranges": [{"startOffset": 26134, "endOffset": 29221, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1920", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/nextSpeakerChecker.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16737, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16737, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkNextSpeaker", "ranges": [{"startOffset": 3142, "endOffset": 6109, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1921", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/geminiChat.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 69486, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 69486, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidResponse", "ranges": [{"startOffset": 2237, "endOffset": 2536, "count": 0}], "isBlockCoverage": false}, {"functionName": "is<PERSON>alid<PERSON><PERSON>nt", "ranges": [{"startOffset": 2537, "endOffset": 2952, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateHistory", "ranges": [{"startOffset": 3140, "endOffset": 3382, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractCuratedHistory", "ranges": [{"startOffset": 3693, "endOffset": 4789, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4991, "endOffset": 23494, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1922", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/retry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 35868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 35868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultShouldRetry", "ranges": [{"startOffset": 1187, "endOffset": 1726, "count": 0}], "isBlockCoverage": false}, {"functionName": "delay", "ranges": [{"startOffset": 1898, "endOffset": 1982, "count": 0}], "isBlockCoverage": false}, {"functionName": "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2290, "endOffset": 8425, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorStatus", "ranges": [{"startOffset": 8585, "endOffset": 9196, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRetryAfterDelayMs", "ranges": [{"startOffset": 9374, "endOffset": 10582, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDelayDurationAndStatus", "ranges": [{"startOffset": 10801, "endOffset": 11060, "count": 0}], "isBlockCoverage": false}, {"functionName": "logRetryAttempt", "ranges": [{"startOffset": 11305, "endOffset": 12428, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1923", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/quotaErrorDetection.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 390, "count": 1}, {"startOffset": 380, "endOffset": 388, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 444, "endOffset": 501, "count": 1}, {"startOffset": 491, "endOffset": 499, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 559, "endOffset": 620, "count": 1}, {"startOffset": 610, "endOffset": 618, "count": 0}], "isBlockCoverage": true}, {"functionName": "isApiError", "ranges": [{"startOffset": 711, "endOffset": 914, "count": 0}], "isBlockCoverage": false}, {"functionName": "isStructuredError", "ranges": [{"startOffset": 916, "endOffset": 1094, "count": 0}], "isBlockCoverage": false}, {"functionName": "isProQuotaExceededError", "ranges": [{"startOffset": 1096, "endOffset": 3182, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGenericQuotaExceededError", "ranges": [{"startOffset": 3184, "endOffset": 3592, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1924", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/loggers.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28116, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28116, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 400, "count": 1}, {"startOffset": 390, "endOffset": 398, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 442, "endOffset": 487, "count": 1}, {"startOffset": 477, "endOffset": 485, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 531, "endOffset": 578, "count": 1}, {"startOffset": 568, "endOffset": 576, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 665, "count": 1}, {"startOffset": 655, "endOffset": 663, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 710, "endOffset": 758, "count": 1}, {"startOffset": 748, "endOffset": 756, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldLogUserPrompts", "ranges": [{"startOffset": 2358, "endOffset": 2408, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2410, "endOffset": 2515, "count": 0}], "isBlockCoverage": false}, {"functionName": "logCliConfiguration", "ranges": [{"startOffset": 2516, "endOffset": 3703, "count": 0}], "isBlockCoverage": false}, {"functionName": "logUserPrompt", "ranges": [{"startOffset": 3705, "endOffset": 4473, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCall", "ranges": [{"startOffset": 4475, "endOffset": 5836, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiRequest", "ranges": [{"startOffset": 5838, "endOffset": 6478, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiError", "ranges": [{"startOffset": 6480, "endOffset": 7878, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponse", "ranges": [{"startOffset": 7880, "endOffset": 9900, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1959", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/sdk.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17109, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17109, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 1}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 371, "endOffset": 424, "count": 1}, {"startOffset": 414, "endOffset": 422, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 472, "endOffset": 523, "count": 1}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "isTelemetrySdkInitialized", "ranges": [{"startOffset": 3850, "endOffset": 3923, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseGrpcEndpoint", "ranges": [{"startOffset": 3925, "endOffset": 4602, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeTelemetry", "ranges": [{"startOffset": 4603, "endOffset": 7042, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdownTelemetry", "ranges": [{"startOffset": 7044, "endOffset": 7473, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2542", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/clearcut-logger/clearcut-logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49427, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49427, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1936, "endOffset": 17139, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1936, "endOffset": 17139, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2543", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23860, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23860, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 3}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 412, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 557, "endOffset": 606, "count": 1}, {"startOffset": 596, "endOffset": 604, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 652, "endOffset": 701, "count": 1}, {"startOffset": 691, "endOffset": 699, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 745, "endOffset": 792, "count": 1}, {"startOffset": 782, "endOffset": 790, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 838, "endOffset": 887, "count": 1}, {"startOffset": 877, "endOffset": 885, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 931, "endOffset": 978, "count": 1}, {"startOffset": 968, "endOffset": 976, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1025, "endOffset": 1075, "count": 1}, {"startOffset": 1065, "endOffset": 1073, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1536, "endOffset": 1696, "count": 1}], "isBlockCoverage": true}, {"functionName": "getDecisionFromOutcome", "ranges": [{"startOffset": 1744, "endOffset": 2414, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2440, "endOffset": 4133, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4157, "endOffset": 4415, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4439, "endOffset": 4813, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4835, "endOffset": 5608, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5632, "endOffset": 5998, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6020, "endOffset": 6562, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6587, "endOffset": 7724, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2544", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/clearcut-logger/event-metadata-key.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17936, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17936, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 579, "endOffset": 8381, "count": 1}], "isBlockCoverage": true}, {"functionName": "getEventMetadataKey", "ranges": [{"startOffset": 8429, "endOffset": 8728, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2545", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/user_id.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8899, "count": 1}, {"startOffset": 1181, "endOffset": 1186, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 354, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureGeminiDirExists", "ranges": [{"startOffset": 1366, "endOffset": 1537, "count": 0}], "isBlockCoverage": false}, {"functionName": "readInstallationIdFromFile", "ranges": [{"startOffset": 1538, "endOffset": 1809, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeInstallationIdToFile", "ranges": [{"startOffset": 1810, "endOffset": 1950, "count": 0}], "isBlockCoverage": false}, {"functionName": "getInstallationId", "ranges": [{"startOffset": 2136, "endOffset": 2597, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGoogleAccountId", "ranges": [{"startOffset": 2902, "endOffset": 3559, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2546", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/uiTelemetry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16712, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16712, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 408, "count": 1}, {"startOffset": 398, "endOffset": 406, "count": 0}], "isBlockCoverage": true}, {"functionName": "createInitialModelMetrics", "ranges": [{"startOffset": 1117, "endOffset": 1361, "count": 0}], "isBlockCoverage": false}, {"functionName": "createInitialMetrics", "ranges": [{"startOffset": 1392, "endOffset": 1786, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1834, "endOffset": 5102, "count": 1}], "isBlockCoverage": true}, {"functionName": "addEvent", "ranges": [{"startOffset": 1910, "endOffset": 2619, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMetrics", "ranges": [{"startOffset": 2624, "endOffset": 2674, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLastPromptTokenCount", "ranges": [{"startOffset": 2679, "endOffset": 2755, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOrCreateModelMetrics", "ranges": [{"startOffset": 2760, "endOffset": 2984, "count": 0}], "isBlockCoverage": false}, {"functionName": "processApiResponse", "ranges": [{"startOffset": 2989, "endOffset": 3653, "count": 0}], "isBlockCoverage": false}, {"functionName": "processApiError", "ranges": [{"startOffset": 3658, "endOffset": 3904, "count": 0}], "isBlockCoverage": false}, {"functionName": "processToolCall", "ranges": [{"startOffset": 3909, "endOffset": 5100, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2547", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/tokenLimits.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 394, "count": 1}, {"startOffset": 384, "endOffset": 392, "count": 0}], "isBlockCoverage": true}, {"functionName": "tokenLimit", "ranges": [{"startOffset": 524, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2686", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/services/fileDiscoveryService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 922, "endOffset": 3468, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2687", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/gitIgnoreParser.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8136, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8136, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 938, "endOffset": 2808, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2689", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/services/gitService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14614, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14614, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1582, "endOffset": 5470, "count": 30}], "isBlockCoverage": true}, {"functionName": "GitService", "ranges": [{"startOffset": 1605, "endOffset": 1708, "count": 30}], "isBlockCoverage": true}, {"functionName": "getHistoryDir", "ranges": [{"startOffset": 1713, "endOffset": 1948, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 1953, "endOffset": 2353, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyGitAvailability", "ranges": [{"startOffset": 2358, "endOffset": 2665, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupShadowGitRepository", "ranges": [{"startOffset": 2806, "endOffset": 4407, "count": 0}], "isBlockCoverage": false}, {"functionName": "get shadowGitRepository", "ranges": [{"startOffset": 4412, "endOffset": 4822, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentCommitHash", "ranges": [{"startOffset": 4827, "endOffset": 4969, "count": 0}], "isBlockCoverage": false}, {"functionName": "createFileSnapshot", "ranges": [{"startOffset": 4974, "endOffset": 5184, "count": 0}], "isBlockCoverage": false}, {"functionName": "restoreProjectFromSnapshot", "ranges": [{"startOffset": 5189, "endOffset": 5468, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2696", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/memoryDiscovery.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30520, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30520, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 265, "endOffset": 327, "count": 1}, {"startOffset": 317, "endOffset": 325, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 1539, "endOffset": 1603, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1678, "endOffset": 1740, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1816, "endOffset": 1880, "count": 0}], "isBlockCoverage": false}, {"functionName": "findProjectRoot", "ranges": [{"startOffset": 1932, "endOffset": 3025, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGeminiMdFilePathsInternal", "ranges": [{"startOffset": 3026, "endOffset": 6739, "count": 0}], "isBlockCoverage": false}, {"functionName": "readGeminiMdFiles", "ranges": [{"startOffset": 6740, "endOffset": 7792, "count": 0}], "isBlockCoverage": false}, {"functionName": "concatenateInstructions", "ranges": [{"startOffset": 7793, "endOffset": 8432, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadServerHierarchicalMemory", "ranges": [{"startOffset": 8433, "endOffset": 9520, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2697", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/bfsFileSearch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 772, "endOffset": 834, "count": 0}], "isBlockCoverage": false}, {"functionName": "bfsFileSearch", "ranges": [{"startOffset": 1124, "endOffset": 2565, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2698", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/memoryImportProcessor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22070, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22070, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 348, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 830, "endOffset": 894, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 973, "endOffset": 1035, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1115, "endOffset": 1179, "count": 0}], "isBlockCoverage": false}, {"functionName": "processImports", "ranges": [{"startOffset": 1627, "endOffset": 6611, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateImportPath", "ranges": [{"startOffset": 6934, "endOffset": 7386, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2699", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6384, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6384, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 1}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 521, "count": 1}, {"startOffset": 511, "endOffset": 519, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 571, "endOffset": 646, "count": 1}, {"startOffset": 636, "endOffset": 644, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 694, "endOffset": 767, "count": 1}, {"startOffset": 757, "endOffset": 765, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 823, "endOffset": 904, "count": 1}, {"startOffset": 894, "endOffset": 902, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 954, "endOffset": 1029, "count": 1}, {"startOffset": 1019, "endOffset": 1027, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1073, "endOffset": 1142, "count": 1}, {"startOffset": 1132, "endOffset": 1140, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1184, "endOffset": 1251, "count": 1}, {"startOffset": 1241, "endOffset": 1249, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1295, "endOffset": 1364, "count": 1}, {"startOffset": 1354, "endOffset": 1362, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1406, "endOffset": 1473, "count": 1}, {"startOffset": 1463, "endOffset": 1471, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1518, "endOffset": 1588, "count": 1}, {"startOffset": 1578, "endOffset": 1586, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1636, "endOffset": 1709, "count": 1}, {"startOffset": 1699, "endOffset": 1707, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1826, "count": 1}, {"startOffset": 1816, "endOffset": 1824, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1872, "endOffset": 1943, "count": 1}, {"startOffset": 1933, "endOffset": 1941, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1987, "endOffset": 2056, "count": 1}, {"startOffset": 2046, "endOffset": 2054, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2102, "endOffset": 2173, "count": 1}, {"startOffset": 2163, "endOffset": 2171, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2217, "endOffset": 2286, "count": 1}, {"startOffset": 2276, "endOffset": 2284, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2405, "count": 1}, {"startOffset": 2395, "endOffset": 2403, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2450, "endOffset": 2520, "count": 1}, {"startOffset": 2510, "endOffset": 2518, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2560, "endOffset": 2625, "count": 1}, {"startOffset": 2615, "endOffset": 2623, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2674, "endOffset": 2748, "count": 1}, {"startOffset": 2738, "endOffset": 2746, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4190, "endOffset": 4296, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2700", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32748, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32748, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 382, "count": 1}, {"startOffset": 372, "endOffset": 380, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1088, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1151, "endOffset": 10548, "count": 30}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON>", "ranges": [{"startOffset": 1385, "endOffset": 1451, "count": 30}], "isBlockCoverage": true}, {"functionName": "_readLogFile", "ranges": [{"startOffset": 1456, "endOffset": 2890, "count": 0}], "isBlockCoverage": false}, {"functionName": "_backupCorruptedLogFile", "ranges": [{"startOffset": 2895, "endOffset": 3417, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 3422, "endOffset": 4612, "count": 0}], "isBlockCoverage": false}, {"functionName": "_updateLogFile", "ranges": [{"startOffset": 4617, "endOffset": 6965, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPreviousUserMessages", "ranges": [{"startOffset": 6970, "endOffset": 7395, "count": 0}], "isBlockCoverage": false}, {"functionName": "logMessage", "ranges": [{"startOffset": 7400, "endOffset": 8551, "count": 0}], "isBlockCoverage": false}, {"functionName": "_checkpointPath", "ranges": [{"startOffset": 8556, "endOffset": 8880, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveCheckpoint", "ranges": [{"startOffset": 8885, "endOffset": 9371, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCheckpoint", "ranges": [{"startOffset": 9376, "endOffset": 10366, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 10371, "endOffset": 10546, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2701", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/geminiRequest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 1}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "partListUnionToString", "ranges": [{"startOffset": 404, "endOffset": 1583, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2702", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/coreToolScheduler.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 63100, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 63100, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 1}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 420, "count": 1}, {"startOffset": 410, "endOffset": 418, "count": 0}], "isBlockCoverage": true}, {"functionName": "createFunctionResponsePart", "ranges": [{"startOffset": 1313, "endOffset": 1523, "count": 0}], "isBlockCoverage": false}, {"functionName": "convertToFunctionResponse", "ranges": [{"startOffset": 1524, "endOffset": 3270, "count": 0}], "isBlockCoverage": false}, {"functionName": "createErrorResponse", "ranges": [{"startOffset": 3300, "endOffset": 3577, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3603, "endOffset": 18668, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2703", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/modifiable-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14519, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14519, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModifiableTool", "ranges": [{"startOffset": 1494, "endOffset": 1568, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTempFilesForModify", "ranges": [{"startOffset": 1570, "endOffset": 2615, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUpdatedParams", "ranges": [{"startOffset": 2616, "endOffset": 3645, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteTempFiles", "ranges": [{"startOffset": 3646, "endOffset": 4015, "count": 0}], "isBlockCoverage": false}, {"functionName": "modifyWithEditor", "ranges": [{"startOffset": 4206, "endOffset": 4871, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2704", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/editor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 420, "count": 1}, {"startOffset": 410, "endOffset": 418, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 519, "count": 1}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 564, "endOffset": 612, "count": 1}, {"startOffset": 602, "endOffset": 610, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 651, "endOffset": 693, "count": 1}, {"startOffset": 683, "endOffset": 691, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidEditorType", "ranges": [{"startOffset": 1033, "endOffset": 1232, "count": 0}], "isBlockCoverage": false}, {"functionName": "commandExists", "ranges": [{"startOffset": 1233, "endOffset": 1453, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkHasEditorType", "ranges": [{"startOffset": 1836, "endOffset": 2056, "count": 0}], "isBlockCoverage": false}, {"functionName": "allowEditorTypeInSandbox", "ranges": [{"startOffset": 2058, "endOffset": 2289, "count": 0}], "isBlockCoverage": false}, {"functionName": "isEditorAvailable", "ranges": [{"startOffset": 2448, "endOffset": 2637, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDiffCommand", "ranges": [{"startOffset": 2694, "endOffset": 4635, "count": 0}], "isBlockCoverage": false}, {"functionName": "openDiff", "ranges": [{"startOffset": 4847, "endOffset": 6752, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2705", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/nonInteractiveToolExecutor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12338, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12338, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "executeToolCall", "ranges": [{"startOffset": 867, "endOffset": 4170, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2706", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/session.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1267, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1267, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2707", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10774, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10774, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 345, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 439, "endOffset": 487, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 574, "count": 186}, {"startOffset": 564, "endOffset": 572, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 703, "endOffset": 911, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 975, "endOffset": 1131, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1195, "endOffset": 1505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1565, "endOffset": 1989, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2708", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/generated/git-commit.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1094, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1094, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 4}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2709", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/utils/formatters.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5378, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5378, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 2}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 398, "count": 3}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatMemoryUsage", "ranges": [{"startOffset": 515, "endOffset": 780, "count": 2}, {"startOffset": 598, "endOffset": 649, "count": 0}, {"startOffset": 745, "endOffset": 779, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatDuration", "ranges": [{"startOffset": 805, "endOffset": 1577, "count": 3}, {"startOffset": 850, "endOffset": 872, "count": 0}, {"startOffset": 900, "endOffset": 949, "count": 0}, {"startOffset": 1019, "endOffset": 1066, "count": 0}, {"startOffset": 1435, "endOffset": 1548, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2710", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/services/CommandService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3297, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3297, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 2}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadBuiltInCommands", "ranges": [{"startOffset": 808, "endOffset": 936, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 959, "endOffset": 1201, "count": 5}], "isBlockCoverage": true}, {"functionName": "CommandService", "ranges": [{"startOffset": 963, "endOffset": 1057, "count": 5}], "isBlockCoverage": true}, {"functionName": "loadCommands", "ranges": [{"startOffset": 1077, "endOffset": 1151, "count": 5}], "isBlockCoverage": true}, {"functionName": "getCommands", "ranges": [{"startOffset": 1154, "endOffset": 1199, "count": 5}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2711", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/commands/memoryCommand.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 870, "endOffset": 1400, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 1496, "endOffset": 2073, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 2182, "endOffset": 3357, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2712", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/commands/helpCommand.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 489, "endOffset": 620, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2713", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/commands/clearCommand.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1922, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1922, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "action", "ranges": [{"startOffset": 496, "endOffset": 691, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}