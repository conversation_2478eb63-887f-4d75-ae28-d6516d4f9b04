{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/useGitBranchName.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23426, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23426, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 498, "endOffset": 602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 655, "endOffset": 768, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1658, "endOffset": 8068, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1705, "endOffset": 1884, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1925, "endOffset": 2031, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2094, "endOffset": 2594, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2159, "endOffset": 2293, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2361, "endOffset": 2404, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2437, "endOffset": 2521, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2676, "endOffset": 3273, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2741, "endOffset": 2899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2967, "endOffset": 3010, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3113, "endOffset": 3197, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3377, "endOffset": 4061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3442, "endOffset": 3757, "count": 2}, {"startOffset": 3536, "endOffset": 3697, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3825, "endOffset": 3868, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3901, "endOffset": 3985, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4170, "endOffset": 4875, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4235, "endOffset": 4571, "count": 2}, {"startOffset": 4329, "endOffset": 4511, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4639, "endOffset": 4682, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4715, "endOffset": 4799, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4961, "endOffset": 5954, "count": 1}, {"startOffset": 5414, "endOffset": 5953, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5050, "endOffset": 5184, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5252, "endOffset": 5295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5328, "endOffset": 5412, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5537, "endOffset": 5674, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5712, "endOffset": 5878, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6034, "endOffset": 7143, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6150, "endOffset": 6284, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6352, "endOffset": 6395, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6428, "endOffset": 6512, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6637, "endOffset": 6774, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6904, "endOffset": 7070, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7214, "endOffset": 8064, "count": 1}, {"startOffset": 7848, "endOffset": 8063, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7483, "endOffset": 7617, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7686, "endOffset": 7729, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7762, "endOffset": 7846, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1323", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/useGitBranchName.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7640, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7640, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 14}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "useGitBranchName", "ranges": [{"startOffset": 1156, "endOffset": 2760, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1340, "endOffset": 2007, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1414, "endOffset": 2001, "count": 5}, {"startOffset": 1463, "endOffset": 1525, "count": 1}, {"startOffset": 1525, "endOffset": 1615, "count": 4}, {"startOffset": 1617, "endOffset": 1993, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1760, "endOffset": 1970, "count": 2}, {"startOffset": 1819, "endOffset": 1969, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2077, "endOffset": 2711, "count": 5}], "isBlockCoverage": true}, {"functionName": "setupWatcher", "ranges": [{"startOffset": 2243, "endOffset": 2636, "count": 5}, {"startOffset": 2383, "endOffset": 2601, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2455, "endOffset": 2590, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2669, "endOffset": 2706, "count": 5}, {"startOffset": 2690, "endOffset": 2697, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}]}