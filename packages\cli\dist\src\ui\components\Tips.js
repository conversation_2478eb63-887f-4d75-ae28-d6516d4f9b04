import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
export const Tips = ({ config }) => {
    const geminiMdFileCount = config.getGeminiMdFileCount();
    return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Welcome to Fumbly AI CLI!" }), " Tips for getting started:"] }), _jsx(Text, { color: Colors.Foreground, children: "1. Ask questions, edit files, or run commands naturally." }), _jsxs(Text, { color: Colors.Foreground, children: ["2. Use ", _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "@filename" }), " to include files in context."] }), _jsxs(Text, { color: Colors.Foreground, children: ["3. Try ", _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "/model list" }), " to see available AI models."] }), _jsxs(Text, { color: Colors.Foreground, children: ["4. Use ", _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Ctrl+X" }), " or ", _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "F2" }), " to open external editor for complex prompts."] }), _jsxs(Text, { color: Colors.Foreground, children: ["5. Type ", _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "/help" }), " for more commands and shortcuts."] }), geminiMdFileCount === 0 && (_jsxs(Text, { color: Colors.Foreground, children: ["6. Create", ' ', _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "GEMINI.md" }), ' ', "files to customize your interactions with the AI."] })), _jsxs(Text, { color: Colors.Foreground, children: [geminiMdFileCount === 0 ? '4.' : '3.', ' ', _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "/help" }), ' ', "for more information."] })] }));
};
//# sourceMappingURL=Tips.js.map