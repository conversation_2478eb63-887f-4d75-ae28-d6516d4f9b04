{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/useInputHistory.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24474, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24474, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 610, "endOffset": 8212, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 730, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 877, "endOffset": 1296, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 952, "endOffset": 1149, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1191, "endOffset": 1241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1326, "endOffset": 2560, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1402, "endOffset": 2035, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1481, "endOffset": 1700, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1746, "endOffset": 1818, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1922, "endOffset": 1976, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2106, "endOffset": 2554, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2185, "endOffset": 2390, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2436, "endOffset": 2495, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2588, "endOffset": 5914, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2647, "endOffset": 3153, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2726, "endOffset": 2939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2985, "endOffset": 3094, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3211, "endOffset": 3720, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3290, "endOffset": 3506, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3552, "endOffset": 3661, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3814, "endOffset": 4310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3937, "endOffset": 4138, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4184, "endOffset": 4236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4395, "endOffset": 5053, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4524, "endOffset": 4725, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4771, "endOffset": 4823, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4928, "endOffset": 4982, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5138, "endOffset": 5908, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5217, "endOffset": 5422, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5468, "endOffset": 5520, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5625, "endOffset": 5677, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5782, "endOffset": 5834, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5944, "endOffset": 8208, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6003, "endOffset": 6818, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.initialProps", "ranges": [{"startOffset": 6322, "endOffset": 6381, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6471, "endOffset": 6523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6648, "endOffset": 6759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6901, "endOffset": 7408, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6980, "endOffset": 7192, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7238, "endOffset": 7349, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7497, "endOffset": 8202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7625, "endOffset": 7841, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7887, "endOffset": 7939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8076, "endOffset": 8130, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1319", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/useInputHistory.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8609, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8609, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 21}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "useInputHistory", "ranges": [{"startOffset": 557, "endOffset": 2665, "count": 21}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 904, "endOffset": 975, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1048, "endOffset": 1199, "count": 2}, {"startOffset": 1126, "endOffset": 1167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1297, "endOffset": 1872, "count": 9}, {"startOffset": 1324, "endOffset": 1337, "count": 1}, {"startOffset": 1337, "endOffset": 1373, "count": 8}, {"startOffset": 1373, "endOffset": 1386, "count": 1}, {"startOffset": 1386, "endOffset": 1450, "count": 7}, {"startOffset": 1450, "endOffset": 1525, "count": 5}, {"startOffset": 1525, "endOffset": 1651, "count": 2}, {"startOffset": 1618, "endOffset": 1651, "count": 0}, {"startOffset": 1651, "endOffset": 1849, "count": 7}, {"startOffset": 1849, "endOffset": 1871, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2111, "endOffset": 2475, "count": 6}, {"startOffset": 2138, "endOffset": 2151, "count": 1}, {"startOffset": 2151, "endOffset": 2181, "count": 5}, {"startOffset": 2181, "endOffset": 2194, "count": 3}, {"startOffset": 2194, "endOffset": 2340, "count": 2}, {"startOffset": 2340, "endOffset": 2453, "count": 0}, {"startOffset": 2453, "endOffset": 2474, "count": 2}], "isBlockCoverage": true}], "startOffset": 209}]}