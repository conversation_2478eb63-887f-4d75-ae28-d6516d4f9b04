{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/useHistoryManager.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22721, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22721, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 806, "endOffset": 7987, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 888, "endOffset": 1080, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 956, "endOffset": 1000, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1164, "endOffset": 1923, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1232, "endOffset": 1276, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1456, "endOffset": 1520, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2032, "endOffset": 2965, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2100, "endOffset": 2144, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2466, "endOffset": 2595, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3041, "endOffset": 3829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3109, "endOffset": 3153, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3366, "endOffset": 3442, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3522, "endOffset": 3599, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3946, "endOffset": 4572, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4014, "endOffset": 4058, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4238, "endOffset": 4302, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4396, "endOffset": 4479, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4634, "endOffset": 5402, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4702, "endOffset": 4746, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5042, "endOffset": 5159, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5274, "endOffset": 5322, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5490, "endOffset": 6772, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5558, "endOffset": 5602, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6166, "endOffset": 6399, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6872, "endOffset": 7983, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6940, "endOffset": 6984, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7452, "endOffset": 7629, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1320", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/useHistoryManager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8735, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8735, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 18}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "useHistory", "ranges": [{"startOffset": 556, "endOffset": 2231, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 784, "endOffset": 902, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 969, "endOffset": 1018, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1086, "endOffset": 1580, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1227, "endOffset": 1555, "count": 14}, {"startOffset": 1282, "endOffset": 1504, "count": 7}, {"startOffset": 1387, "endOffset": 1413, "count": 5}, {"startOffset": 1414, "endOffset": 1447, "count": 1}, {"startOffset": 1449, "endOffset": 1494, "count": 1}, {"startOffset": 1504, "endOffset": 1554, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1674, "endOffset": 1992, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1719, "endOffset": 1977, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1752, "endOffset": 1976, "count": 2}, {"startOffset": 1794, "endOffset": 1942, "count": 1}, {"startOffset": 1857, "endOffset": 1872, "count": 0}, {"startOffset": 1942, "endOffset": 1975, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2065, "endOffset": 2133, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}]}