{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH;;GAEG;AACH,MAAa,iBAAkB,SAAQ,KAAK;IAK1C,YAAY,OAAgB,EAAE,IAAa,EAAE,IAAa;QACxD,KAAK,CAAC,OAAO,CAAC,CAAC;QAJC,SAAI,GAAW,mBAAmB,CAAC;QAKnD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;CACF;AAVD,8CAUC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Interface for handling error\n */\nexport class OTLPExporterError extends Error {\n  readonly code?: number;\n  override readonly name: string = 'OTLPExporterError';\n  readonly data?: string;\n\n  constructor(message?: string, code?: number, data?: string) {\n    super(message);\n    this.data = data;\n    this.code = code;\n  }\n}\n\n/**\n * Interface for handling export service errors\n */\nexport interface ExportServiceError {\n  name: string;\n  code: number;\n  details: string;\n  metadata: { [key: string]: unknown };\n  message: string;\n  stack: string;\n}\n\n/**\n * Collector Exporter base config\n */\nexport interface OTLPExporterConfigBase {\n  headers?: Partial<Record<string, unknown>>;\n  hostname?: string;\n  url?: string;\n  concurrencyLimit?: number;\n  /** Maximum time the OTLP exporter will wait for each batch export.\n   * The default value is 10000ms. */\n  timeoutMillis?: number;\n}\n"]}