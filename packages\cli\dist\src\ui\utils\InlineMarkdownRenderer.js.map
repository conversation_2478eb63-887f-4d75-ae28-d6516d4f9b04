{"version": 3, "file": "InlineMarkdownRenderer.js", "sourceRoot": "", "sources": ["../../../../src/ui/utils/InlineMarkdownRenderer.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAC3B,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,WAAW,MAAM,cAAc,CAAC;AAEvC,iCAAiC;AACjC,MAAM,kBAAkB,GAAG,CAAC,CAAC,CAAC,WAAW;AACzC,MAAM,oBAAoB,GAAG,CAAC,CAAC,CAAC,iBAAiB;AACjD,MAAM,2BAA2B,GAAG,CAAC,CAAC,CAAC,WAAW;AAClD,MAAM,yBAAyB,GAAG,CAAC,CAAC,CAAC,UAAU;AAC/C,MAAM,0BAA0B,GAAG,CAAC,CAAC,CAAC,YAAY;AAClD,MAAM,wBAAwB,GAAG,CAAC,CAAC,CAAC,aAAa;AAMjD,MAAM,oBAAoB,GAAgC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;IACrE,MAAM,KAAK,GAAsB,EAAE,CAAC;IACpC,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,MAAM,WAAW,GACf,yEAAyE,CAAC;IAC5E,IAAI,KAAK,CAAC;IAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QACjD,IAAI,KAAK,CAAC,KAAK,GAAG,SAAS,EAAE,CAAC;YAC5B,KAAK,CAAC,IAAI,CACR,KAAC,IAAI,cACF,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAD1B,KAAK,SAAS,EAAE,CAEpB,CACR,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,YAAY,GAAoB,IAAI,CAAC;QACzC,MAAM,GAAG,GAAG,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC;QAE/B,IAAI,CAAC;YACH,IACE,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC;gBAC1B,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACxB,SAAS,CAAC,MAAM,GAAG,kBAAkB,GAAG,CAAC,EACzC,CAAC;gBACD,YAAY,GAAG,CACb,KAAC,IAAI,IAAW,IAAI,kBACjB,SAAS,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,CAAC,IADhD,GAAG,CAEP,CACR,CAAC;YACJ,CAAC;iBAAM,IACL,SAAS,CAAC,MAAM,GAAG,oBAAoB,GAAG,CAAC;gBAC3C,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oBACrD,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;gBACzD,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBACxD,CAAC,IAAI,CAAC,IAAI,CACR,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC,CACjE;gBACD,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC9D,CAAC,UAAU,CAAC,IAAI,CACd,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC,CACjE,EACD,CAAC;gBACD,YAAY,GAAG,CACb,KAAC,IAAI,IAAW,MAAM,kBACnB,SAAS,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,oBAAoB,CAAC,IADpD,GAAG,CAEP,CACR,CAAC;YACJ,CAAC;iBAAM,IACL,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC;gBAC1B,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACxB,SAAS,CAAC,MAAM,GAAG,2BAA2B,GAAG,CAAC,EAClD,CAAC;gBACD,YAAY,GAAG,CACb,KAAC,IAAI,IAAW,aAAa,kBAC1B,SAAS,CAAC,KAAK,CACd,2BAA2B,EAC3B,CAAC,2BAA2B,CAC7B,IAJQ,GAAG,CAKP,CACR,CAAC;YACJ,CAAC;iBAAM,IACL,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC;gBACzB,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACvB,SAAS,CAAC,MAAM,GAAG,yBAAyB,EAC5C,CAAC;gBACD,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBACpD,IAAI,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC9B,YAAY,GAAG,CACb,KAAC,IAAI,IAAW,KAAK,EAAE,MAAM,CAAC,YAAY,YACvC,SAAS,CAAC,CAAC,CAAC,IADJ,GAAG,CAEP,CACR,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,IACL,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC;gBACzB,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACxB,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EACvB,CAAC;gBACD,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;gBACxD,IAAI,SAAS,EAAE,CAAC;oBACd,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC9B,MAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBACzB,YAAY,GAAG,CACb,MAAC,IAAI,eACF,QAAQ,EACT,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,mBAAK,GAAG,SAAS,KAFtC,GAAG,CAGP,CACR,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,IACL,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC;gBAC3B,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC1B,SAAS,CAAC,MAAM;oBACd,0BAA0B,GAAG,wBAAwB,GAAG,CAAC,CAAC,yEAAyE;cACrI,CAAC;gBACD,YAAY,GAAG,CACb,KAAC,IAAI,IAAW,SAAS,kBACtB,SAAS,CAAC,KAAK,CACd,0BAA0B,EAC1B,CAAC,wBAAwB,CAC1B,IAJQ,GAAG,CAKP,CACR,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;YACnE,YAAY,GAAG,IAAI,CAAC;QACtB,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,YAAY,IAAI,KAAC,IAAI,cAAY,SAAS,IAAf,GAAG,CAAoB,CAAC,CAAC;QAC/D,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;IACpC,CAAC;IAED,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC5B,KAAK,CAAC,IAAI,CAAC,KAAC,IAAI,cAAyB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAxC,KAAK,SAAS,EAAE,CAAgC,CAAC,CAAC;IAC1E,CAAC;IAED,OAAO,4BAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,GAAI,CAAC;AACtD,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;AAE7D;;;GAGG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,IAAY,EAAU,EAAE;IACzD,MAAM,SAAS,GAAG,IAAI;SACnB,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC;SAC/B,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC;SAC3B,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;SACzB,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC;SAC3B,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;SACzB,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC;SAC/B,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;IACtC,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC;AAChC,CAAC,CAAC"}