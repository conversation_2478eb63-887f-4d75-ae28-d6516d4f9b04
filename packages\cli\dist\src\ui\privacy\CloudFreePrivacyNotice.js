import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Box, Newline, Text, useInput } from 'ink';
import { RadioButtonSelect } from '../components/shared/RadioButtonSelect.js';
import { usePrivacySettings } from '../hooks/usePrivacySettings.js';
import { CloudPaidPrivacyNotice } from './CloudPaidPrivacyNotice.js';
import { Colors } from '../colors.js';
export const CloudFreePrivacyNotice = ({ config, onExit, }) => {
    const { privacyState, updateDataCollectionOptIn } = usePrivacySettings(config);
    useInput((input, key) => {
        if (privacyState.error && key.escape) {
            onExit();
        }
    });
    if (privacyState.isLoading) {
        return _jsx(Text, { color: Colors.Gray, children: "Loading..." });
    }
    if (privacyState.error) {
        return (_jsxs(Box, { flexDirection: "column", marginY: 1, children: [_jsxs(Text, { color: Colors.AccentRed, children: ["Error loading Opt-in settings: ", privacyState.error] }), _jsx(Text, { color: Colors.Gray, children: "Press Esc to exit." })] }));
    }
    if (privacyState.isFreeTier === false) {
        return _jsx(CloudPaidPrivacyNotice, { onExit: onExit });
    }
    const items = [
        { label: 'Yes', value: true },
        { label: 'No', value: false },
    ];
    return (_jsxs(Box, { flexDirection: "column", marginY: 1, children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Gemini Code Assist for Individuals Privacy Notice" }), _jsx(Newline, {}), _jsxs(Text, { children: ["This notice and our Privacy Policy", _jsx(Text, { color: Colors.AccentBlue, children: "[1]" }), " describe how Gemini Code Assist handles your data. Please read them carefully."] }), _jsx(Newline, {}), _jsx(Text, { children: "When you use Gemini Code Assist for individuals with Gemini CLI, Google collects your prompts, related code, generated output, code edits, related feature usage information, and your feedback to provide, improve, and develop Google products and services and machine learning technologies." }), _jsx(Newline, {}), _jsx(Text, { children: "To help with quality and improve our products (such as generative machine-learning models), human reviewers may read, annotate, and process the data collected above. We take steps to protect your privacy as part of this process. This includes disconnecting the data from your Google Account before reviewers see or annotate it, and storing those disconnected copies for up to 18 months. Please don't submit confidential information or any data you wouldn't want a reviewer to see or Google to use to improve our products, services and machine-learning technologies." }), _jsx(Newline, {}), _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: "Allow Google to use this data to develop and improve our products?" }), _jsx(RadioButtonSelect, { items: items, initialIndex: privacyState.dataCollectionOptIn ? 0 : 1, onSelect: (value) => {
                            updateDataCollectionOptIn(value);
                            // Only exit if there was no error.
                            if (!privacyState.error) {
                                onExit();
                            }
                        } })] }), _jsx(Newline, {}), _jsxs(Text, { children: [_jsx(Text, { color: Colors.AccentBlue, children: "[1]" }), ' ', "https://policies.google.com/privacy"] }), _jsx(Newline, {}), _jsx(Text, { color: Colors.Gray, children: "Press Enter to choose an option and exit." })] }));
};
//# sourceMappingURL=CloudFreePrivacyNotice.js.map