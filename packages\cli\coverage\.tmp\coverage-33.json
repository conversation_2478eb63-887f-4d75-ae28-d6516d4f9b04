{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/useLoadingIndicator.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16394, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16394, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1132, "endOffset": 6536, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1179, "endOffset": 1236, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1277, "endOffset": 1422, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1365, "endOffset": 1416, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1507, "endOffset": 1889, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1582, "endOffset": 1676, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1964, "endOffset": 2700, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2045, "endOffset": 2145, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2413, "endOffset": 2548, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2815, "endOffset": 3855, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.streamingState", "ranges": [{"startOffset": 2906, "endOffset": 2991, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3132, "endOffset": 3219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3331, "endOffset": 3441, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3687, "endOffset": 3774, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4001, "endOffset": 5393, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.streamingState", "ranges": [{"startOffset": 4092, "endOffset": 4177, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4318, "endOffset": 4405, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4516, "endOffset": 4626, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4865, "endOffset": 4963, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5226, "endOffset": 5313, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5512, "endOffset": 6532, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.streamingState", "ranges": [{"startOffset": 5603, "endOffset": 5688, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5829, "endOffset": 5916, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6028, "endOffset": 6120, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6365, "endOffset": 6452, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1320", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/useLoadingIndicator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7023, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7023, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 24}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "useLoadingIndicator", "ranges": [{"startOffset": 968, "endOffset": 2738, "count": 24}, {"startOffset": 2661, "endOffset": 2682, "count": 4}, {"startOffset": 2683, "endOffset": 2705, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1787, "endOffset": 2511, "count": 16}, {"startOffset": 1897, "endOffset": 1966, "count": 1}, {"startOffset": 1968, "endOffset": 2058, "count": 1}, {"startOffset": 2058, "endOffset": 2454, "count": 15}, {"startOffset": 2129, "endOffset": 2213, "count": 3}, {"startOffset": 2215, "endOffset": 2305, "count": 1}, {"startOffset": 2305, "endOffset": 2454, "count": 14}, {"startOffset": 2395, "endOffset": 2454, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1993, "endOffset": 2017, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2240, "endOffset": 2264, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1321", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/types.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10774, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10774, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 156}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 345, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 439, "endOffset": 487, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 574, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 703, "endOffset": 911, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 975, "endOffset": 1131, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1195, "endOffset": 1505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1565, "endOffset": 1989, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1322", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/useTimer.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5648, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5648, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 24}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "useTimer", "ranges": [{"startOffset": 567, "endOffset": 1795, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 909, "endOffset": 1747, "count": 11}, {"startOffset": 996, "endOffset": 1075, "count": 2}, {"startOffset": 1119, "endOffset": 1130, "count": 3}, {"startOffset": 1132, "endOffset": 1169, "count": 1}, {"startOffset": 1196, "endOffset": 1228, "count": 3}, {"startOffset": 1288, "endOffset": 1479, "count": 6}, {"startOffset": 1318, "endOffset": 1368, "count": 0}, {"startOffset": 1479, "endOffset": 1604, "count": 5}, {"startOffset": 1515, "endOffset": 1598, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1407, "endOffset": 1466, "count": 91}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1438, "endOffset": 1456, "count": 91}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1617, "endOffset": 1742, "count": 11}, {"startOffset": 1653, "endOffset": 1736, "count": 6}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1325", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/usePhraseCycler.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21883, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21883, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 5}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 428, "count": 1}, {"startOffset": 418, "endOffset": 426, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 474, "endOffset": 523, "count": 24}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "usePhraseCycler", "ranges": [{"startOffset": 6431, "endOffset": 7942, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6692, "endOffset": 7884, "count": 9}, {"startOffset": 6719, "endOffset": 6932, "count": 2}, {"startOffset": 6825, "endOffset": 6926, "count": 0}, {"startOffset": 6932, "endOffset": 7714, "count": 7}, {"startOffset": 6952, "endOffset": 7504, "count": 5}, {"startOffset": 6991, "endOffset": 7050, "count": 0}, {"startOffset": 7504, "endOffset": 7714, "count": 2}, {"startOffset": 7549, "endOffset": 7650, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7279, "endOffset": 7469, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7727, "endOffset": 7879, "count": 9}, {"startOffset": 7772, "endOffset": 7873, "count": 5}], "isBlockCoverage": true}], "startOffset": 209}]}