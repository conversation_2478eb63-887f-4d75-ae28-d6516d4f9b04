{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/usePhraseCycler.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17883, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17883, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 889, "endOffset": 6741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 936, "endOffset": 993, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1034, "endOffset": 1093, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1208, "endOffset": 1450, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1276, "endOffset": 1337, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1557, "endOffset": 1952, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.isActive", "ranges": [{"startOffset": 1642, "endOffset": 1733, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2051, "endOffset": 2448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2119, "endOffset": 2180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2217, "endOffset": 2335, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2558, "endOffset": 3426, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2626, "endOffset": 2686, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2879, "endOffset": 2997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3189, "endOffset": 3307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3557, "endOffset": 5165, "count": 1}, {"startOffset": 3629, "endOffset": 3650, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3745, "endOffset": 3881, "count": 3}], "isBlockCoverage": true}, {"functionName": "initialProps.isActive", "ranges": [{"startOffset": 3961, "endOffset": 4052, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4481, "endOffset": 4599, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4985, "endOffset": 5052, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5254, "endOffset": 5569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5323, "endOffset": 5383, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5679, "endOffset": 6737, "count": 1}], "isBlockCoverage": true}, {"functionName": "initialProps.isActive", "ranges": [{"startOffset": 5764, "endOffset": 5855, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6115, "endOffset": 6233, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1320", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/hooks/usePhraseCycler.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21883, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21883, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 18}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 428, "count": 5}, {"startOffset": 418, "endOffset": 426, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 474, "endOffset": 523, "count": 24}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "usePhraseCycler", "ranges": [{"startOffset": 6431, "endOffset": 7942, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6692, "endOffset": 7884, "count": 13}, {"startOffset": 6719, "endOffset": 6932, "count": 2}, {"startOffset": 6825, "endOffset": 6926, "count": 0}, {"startOffset": 6932, "endOffset": 7714, "count": 11}, {"startOffset": 6952, "endOffset": 7504, "count": 7}, {"startOffset": 6991, "endOffset": 7050, "count": 0}, {"startOffset": 7504, "endOffset": 7714, "count": 4}, {"startOffset": 7549, "endOffset": 7650, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7279, "endOffset": 7469, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7727, "endOffset": 7879, "count": 13}, {"startOffset": 7772, "endOffset": 7873, "count": 7}], "isBlockCoverage": true}], "startOffset": 209}]}