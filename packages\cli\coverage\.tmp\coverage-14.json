{"result": [{"scriptId": "1021", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/nonInteractiveCli.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30650, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30650, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 458, "endOffset": 742, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1100, "endOffset": 10356, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1282, "endOffset": 2505, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1764, "endOffset": 1786, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1874, "endOffset": 1896, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2311, "endOffset": 2321, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2446, "endOffset": 2463, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2546, "endOffset": 2605, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2685, "endOffset": 3705, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2723, "endOffset": 2928, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3787, "endOffset": 5566, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4450, "endOffset": 4523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4547, "endOffset": 4669, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5645, "endOffset": 7505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6339, "endOffset": 6412, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6436, "endOffset": 6593, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6787, "endOffset": 6800, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7603, "endOffset": 8063, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7830, "endOffset": 7843, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8176, "endOffset": 10352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8977, "endOffset": 9050, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9074, "endOffset": 9290, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9484, "endOffset": 9497, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1023", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/nonInteractiveCli.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14333, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14333, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 5}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "getResponseText", "ranges": [{"startOffset": 739, "endOffset": 1224, "count": 8}, {"startOffset": 802, "endOffset": 835, "count": 5}, {"startOffset": 837, "endOffset": 1206, "count": 5}, {"startOffset": 1065, "endOffset": 1095, "count": 0}, {"startOffset": 1206, "endOffset": 1223, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1141, "endOffset": 1160, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1166, "endOffset": 1185, "count": 5}], "isBlockCoverage": true}, {"functionName": "runNonInteractive", "ranges": [{"startOffset": 1225, "endOffset": 4469, "count": 5}, {"startOffset": 1715, "endOffset": 4121, "count": 8}, {"startOffset": 1863, "endOffset": 1868, "count": 0}, {"startOffset": 2148, "endOffset": 2196, "count": 7}, {"startOffset": 2242, "endOffset": 2320, "count": 0}, {"startOffset": 2392, "endOffset": 2445, "count": 5}, {"startOffset": 2479, "endOffset": 2543, "count": 3}, {"startOffset": 2551, "endOffset": 2589, "count": 7}, {"startOffset": 2589, "endOffset": 4048, "count": 3}, {"startOffset": 2702, "endOffset": 2731, "count": 0}, {"startOffset": 2838, "endOffset": 2843, "count": 0}, {"startOffset": 3151, "endOffset": 3520, "count": 2}, {"startOffset": 3382, "endOffset": 3411, "count": 0}, {"startOffset": 3462, "endOffset": 3508, "count": 1}, {"startOffset": 3663, "endOffset": 3693, "count": 0}, {"startOffset": 3779, "endOffset": 3852, "count": 0}, {"startOffset": 4048, "endOffset": 4115, "count": 4}, {"startOffset": 4126, "endOffset": 4324, "count": 1}, {"startOffset": 4397, "endOffset": 4463, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1344, "endOffset": 1419, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1024", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1814, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1814, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 333, "count": 1}, {"startOffset": 323, "endOffset": 331, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 390, "endOffset": 472, "count": 1}, {"startOffset": 462, "endOffset": 470, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 533, "endOffset": 619, "count": 1}, {"startOffset": 609, "endOffset": 617, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1025", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11673, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11673, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 312, "count": 1}, {"startOffset": 302, "endOffset": 310, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1026", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/config/config.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 50707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 50707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 390, "count": 1}, {"startOffset": 380, "endOffset": 388, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 427, "endOffset": 467, "count": 1}, {"startOffset": 457, "endOffset": 465, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 524, "endOffset": 607, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4742, "endOffset": 4889, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4951, "endOffset": 5832, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5847, "endOffset": 17225, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1030", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/contentGenerator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 408, "count": 1}, {"startOffset": 398, "endOffset": 406, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 517, "count": 1}, {"startOffset": 507, "endOffset": 515, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1341, "endOffset": 1555, "count": 1}], "isBlockCoverage": true}, {"functionName": "createContentGeneratorConfig", "ranges": [{"startOffset": 1587, "endOffset": 3116, "count": 0}], "isBlockCoverage": false}, {"functionName": "createContentGenerator", "ranges": [{"startOffset": 3118, "endOffset": 4086, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1237", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/codeAssist.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3709, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3709, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 269, "endOffset": 335, "count": 1}, {"startOffset": 325, "endOffset": 333, "count": 0}], "isBlockCoverage": true}, {"functionName": "createCodeAssistContentGenerator", "ranges": [{"startOffset": 1115, "endOffset": 1677, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1238", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/oauth2.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 346, "endOffset": 396, "count": 1}, {"startOffset": 386, "endOffset": 394, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 451, "endOffset": 509, "count": 1}, {"startOffset": 499, "endOffset": 507, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 565, "endOffset": 624, "count": 1}, {"startOffset": 614, "endOffset": 622, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 676, "endOffset": 731, "count": 1}, {"startOffset": 721, "endOffset": 729, "count": 0}], "isBlockCoverage": true}, {"functionName": "getOauthClient", "ranges": [{"startOffset": 3741, "endOffset": 6279, "count": 0}], "isBlockCoverage": false}, {"functionName": "authWithWeb", "ranges": [{"startOffset": 6281, "endOffset": 9224, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailablePort", "ranges": [{"startOffset": 9225, "endOffset": 9840, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCachedCredentials", "ranges": [{"startOffset": 9842, "endOffset": 10480, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheCredentials", "ranges": [{"startOffset": 10481, "endOffset": 10774, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedCredentialPath", "ranges": [{"startOffset": 10775, "endOffset": 10926, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGoogleAccountIdCachePath", "ranges": [{"startOffset": 10927, "endOffset": 11089, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheGoogleAccountId", "ranges": [{"startOffset": 11090, "endOffset": 11348, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedGoogleAccountId", "ranges": [{"startOffset": 11349, "endOffset": 11715, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearCachedCredentialFile", "ranges": [{"startOffset": 11717, "endOffset": 12029, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRawGoogleAccountId", "ranges": [{"startOffset": 12239, "endOffset": 13379, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1251", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/errors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6685, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6685, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 1}, {"startOffset": 283, "endOffset": 291, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 433, "endOffset": 481, "count": 1}, {"startOffset": 471, "endOffset": 479, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 580, "count": 1}, {"startOffset": 570, "endOffset": 578, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 675, "count": 1}, {"startOffset": 665, "endOffset": 673, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 721, "endOffset": 770, "count": 1}, {"startOffset": 760, "endOffset": 768, "count": 0}], "isBlockCoverage": true}, {"functionName": "isNodeError", "ranges": [{"startOffset": 1030, "endOffset": 1115, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorMessage", "ranges": [{"startOffset": 1117, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "toFriendlyError", "ranges": [{"startOffset": 1459, "endOffset": 2277, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseResponseData", "ranges": [{"startOffset": 2279, "endOffset": 2537, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1259", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8065, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8065, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 398, "count": 0}], "isBlockCoverage": false}, {"functionName": "ProjectIdRequiredError", "ranges": [{"startOffset": 883, "endOffset": 1041, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupUser", "ranges": [{"startOffset": 1147, "endOffset": 2390, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOnboardTier", "ranges": [{"startOffset": 2392, "endOffset": 2777, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1260", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 1}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 404, "count": 1}, {"startOffset": 394, "endOffset": 402, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 456, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 837, "endOffset": 1482, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2147, "endOffset": 2377, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1261", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/server.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23446, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23446, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 422, "count": 1}, {"startOffset": 412, "endOffset": 420, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 469, "endOffset": 519, "count": 1}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1303, "endOffset": 7234, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1266", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/code_assist/converter.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 650, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCountTokenRequest", "ranges": [{"startOffset": 926, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromCountTokenResponse", "ranges": [{"startOffset": 1105, "endOffset": 1203, "count": 0}], "isBlockCoverage": false}, {"functionName": "toGenerateContentRequest", "ranges": [{"startOffset": 1205, "endOffset": 1395, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromGenerateContentResponse", "ranges": [{"startOffset": 1397, "endOffset": 1771, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerateContentRequest", "ranges": [{"startOffset": 1773, "endOffset": 2281, "count": 0}], "isBlockCoverage": false}, {"functionName": "toContents", "ranges": [{"startOffset": 2282, "endOffset": 2515, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2516, "endOffset": 2636, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2637, "endOffset": 3170, "count": 0}], "isBlockCoverage": false}, {"functionName": "toParts", "ranges": [{"startOffset": 3171, "endOffset": 3228, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON>", "ranges": [{"startOffset": 3229, "endOffset": 3370, "count": 0}], "isBlockCoverage": false}, {"functionName": "toVertexGenerationConfig", "ranges": [{"startOffset": 3371, "endOffset": 4380, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1267", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/config/models.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1684, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1684, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 368, "endOffset": 428, "count": 2}, {"startOffset": 418, "endOffset": 426, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 489, "endOffset": 553, "count": 1}, {"startOffset": 543, "endOffset": 551, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1268", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/modelCheck.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7710, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7710, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEffectiveModel", "ranges": [{"startOffset": 1018, "endOffset": 2814, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1269", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/tool-registry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41121, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41121, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 1}, {"startOffset": 289, "endOffset": 297, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 388, "count": 1}, {"startOffset": 378, "endOffset": 386, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 437, "endOffset": 489, "count": 1}, {"startOffset": 479, "endOffset": 487, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1900, "endOffset": 5313, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5334, "endOffset": 12112, "count": 0}], "isBlockCoverage": true}, {"functionName": "sanitizeParameters", "ranges": [{"startOffset": 12713, "endOffset": 12796, "count": 0}], "isBlockCoverage": false}, {"functionName": "_sanitizeParameters", "ranges": [{"startOffset": 12994, "endOffset": 14077, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1270", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/tools.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15463, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15463, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 14}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 398, "count": 1}, {"startOffset": 388, "endOffset": 396, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 571, "endOffset": 3280, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3312, "endOffset": 3750, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1271", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/mcp-client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 261, "endOffset": 319, "count": 1}, {"startOffset": 309, "endOffset": 317, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 365, "endOffset": 414, "count": 1}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 462, "endOffset": 513, "count": 1}, {"startOffset": 503, "endOffset": 511, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 570, "endOffset": 630, "count": 1}, {"startOffset": 620, "endOffset": 628, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 690, "endOffset": 753, "count": 1}, {"startOffset": 743, "endOffset": 751, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 802, "endOffset": 854, "count": 1}, {"startOffset": 844, "endOffset": 852, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 908, "endOffset": 965, "count": 1}, {"startOffset": 955, "endOffset": 963, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1070, "count": 1}, {"startOffset": 1060, "endOffset": 1068, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1117, "endOffset": 1167, "count": 1}, {"startOffset": 1157, "endOffset": 1165, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2902, "endOffset": 3239, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3370, "endOffset": 3707, "count": 1}], "isBlockCoverage": true}, {"functionName": "addMCPStatusChangeListener", "ranges": [{"startOffset": 4074, "endOffset": 4165, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeMCPStatusChangeListener", "ranges": [{"startOffset": 4226, "endOffset": 4415, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateMCPServerStatus", "ranges": [{"startOffset": 4463, "endOffset": 4696, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPServerStatus", "ranges": [{"startOffset": 4748, "endOffset": 4879, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllMCPServerStatuses", "ranges": [{"startOffset": 4920, "endOffset": 5005, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMCPDiscoveryState", "ranges": [{"startOffset": 5054, "endOffset": 5119, "count": 0}], "isBlockCoverage": false}, {"functionName": "discoverMcpTools", "ranges": [{"startOffset": 5121, "endOffset": 6262, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectAndDiscover", "ranges": [{"startOffset": 6794, "endOffset": 15128, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1363", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/mcp-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16421, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16421, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 673, "endOffset": 3111, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 673, "endOffset": 3111, "count": 0}], "isBlockCoverage": true}, {"functionName": "getStringifiedResultForDisplay", "ranges": [{"startOffset": 4240, "endOffset": 5653, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1364", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/ls.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29509, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 29509, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 243, "endOffset": 283, "count": 1}, {"startOffset": 273, "endOffset": 281, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1434, "endOffset": 10361, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1434, "endOffset": 10361, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1365", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/schemaValidator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8007, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8007, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "validate", "ranges": [{"startOffset": 854, "endOffset": 1336, "count": 0}], "isBlockCoverage": false}, {"functionName": "toObjectSchema", "ranges": [{"startOffset": 1599, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1449", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/paths.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 2}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 334, "endOffset": 380, "count": 1}, {"startOffset": 370, "endOffset": 378, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 422, "endOffset": 467, "count": 1}, {"startOffset": 457, "endOffset": 465, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 510, "endOffset": 556, "count": 1}, {"startOffset": 546, "endOffset": 554, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 597, "endOffset": 641, "count": 1}, {"startOffset": 631, "endOffset": 639, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 684, "endOffset": 730, "count": 1}, {"startOffset": 720, "endOffset": 728, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 775, "endOffset": 823, "count": 1}, {"startOffset": 813, "endOffset": 821, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 871, "endOffset": 922, "count": 1}, {"startOffset": 912, "endOffset": 920, "count": 0}], "isBlockCoverage": true}, {"functionName": "tildeify<PERSON><PERSON>", "ranges": [{"startOffset": 1548, "endOffset": 1743, "count": 0}], "isBlockCoverage": false}, {"functionName": "shorten<PERSON>ath", "ranges": [{"startOffset": 1911, "endOffset": 4322, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeRelative", "ranges": [{"startOffset": 4760, "endOffset": 5201, "count": 0}], "isBlockCoverage": false}, {"functionName": "escape<PERSON><PERSON>", "ranges": [{"startOffset": 5245, "endOffset": 5608, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapePath", "ranges": [{"startOffset": 5654, "endOffset": 5731, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectHash", "ranges": [{"startOffset": 5931, "endOffset": 6060, "count": 0}], "isBlockCoverage": false}, {"functionName": "getProjectTempDir", "ranges": [{"startOffset": 6264, "endOffset": 6473, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1450", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/read-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16973, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16973, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1752, "endOffset": 6265, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1752, "endOffset": 6265, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1451", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/fileUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 36964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 495, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 538, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 629, "endOffset": 677, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 790, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSpecificMimeType", "ranges": [{"startOffset": 1719, "endOffset": 1906, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWithinRoot", "ranges": [{"startOffset": 2149, "endOffset": 2941, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBinaryFile", "ranges": [{"startOffset": 3107, "endOffset": 4503, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectFileType", "ranges": [{"startOffset": 4677, "endOffset": 6461, "count": 0}], "isBlockCoverage": false}, {"functionName": "processSingleFileContent", "ranges": [{"startOffset": 6861, "endOffset": 13076, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1458", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/metrics.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18804, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18804, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 426, "endOffset": 477, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 529, "endOffset": 584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 750, "endOffset": 808, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 860, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 971, "endOffset": 1030, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1681, "endOffset": 1825, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2070, "endOffset": 2175, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMeter", "ranges": [{"startOffset": 2176, "endOffset": 2347, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeMetrics", "ranges": [{"startOffset": 2349, "endOffset": 4246, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordToolCallMetrics", "ranges": [{"startOffset": 4248, "endOffset": 4762, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordTokenUsageMetrics", "ranges": [{"startOffset": 4764, "endOffset": 5019, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiResponseMetrics", "ranges": [{"startOffset": 5021, "endOffset": 5537, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordApiErrorMetrics", "ranges": [{"startOffset": 5539, "endOffset": 6083, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordFileOperationMetric", "ranges": [{"startOffset": 6085, "endOffset": 6588, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1554", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/constants.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 440, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 537, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 634, "endOffset": 683, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 784, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 881, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 934, "endOffset": 990, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1045, "endOffset": 1103, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1158, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1273, "endOffset": 1333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1382, "endOffset": 1434, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1597, "endOffset": 1658, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1555", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/grep.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 63513, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 63513, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2499, "endOffset": 22317, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2499, "endOffset": 22317, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1575", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/gitUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6303, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6303, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 388, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGitRepository", "ranges": [{"startOffset": 844, "endOffset": 1653, "count": 0}], "isBlockCoverage": false}, {"functionName": "findGitRoot", "ranges": [{"startOffset": 1842, "endOffset": 2439, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1576", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/glob.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 31867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 31867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 340, "endOffset": 382, "count": 1}, {"startOffset": 372, "endOffset": 380, "count": 0}], "isBlockCoverage": true}, {"functionName": "sortFileEntries", "ranges": [{"startOffset": 1799, "endOffset": 2492, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2624, "endOffset": 11131, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2624, "endOffset": 11131, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1577", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/edit.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 55752, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 55752, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 1}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2389, "endOffset": 19604, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2389, "endOffset": 19604, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1579", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/editCorrector.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 81034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 81034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 473, "endOffset": 531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 578, "endOffset": 628, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 683, "endOffset": 741, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 793, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 905, "endOffset": 965, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1012, "endOffset": 1062, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1127, "endOffset": 1195, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTimestampFromFunctionId", "ranges": [{"startOffset": 3627, "endOffset": 3888, "count": 0}], "isBlockCoverage": false}, {"functionName": "findLastEditTimestamp", "ranges": [{"startOffset": 4254, "endOffset": 6803, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectEdit", "ranges": [{"startOffset": 7332, "endOffset": 13596, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureCorrectFileContent", "ranges": [{"startOffset": 13598, "endOffset": 14191, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctOldStringMismatch", "ranges": [{"startOffset": 14681, "endOffset": 17041, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewString", "ranges": [{"startOffset": 17647, "endOffset": 20453, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctNewStringEscaping", "ranges": [{"startOffset": 20936, "endOffset": 23240, "count": 0}], "isBlockCoverage": false}, {"functionName": "correctStringEscaping", "ranges": [{"startOffset": 23672, "endOffset": 25581, "count": 0}], "isBlockCoverage": false}, {"functionName": "trimPairIfPossible", "ranges": [{"startOffset": 25583, "endOffset": 26243, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapeStringForGeminiBug", "ranges": [{"startOffset": 26321, "endOffset": 28630, "count": 0}], "isBlockCoverage": false}, {"functionName": "countOccurrences", "ranges": [{"startOffset": 28689, "endOffset": 28998, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetEditCorrectorCaches_TEST_ONLY", "ranges": [{"startOffset": 29000, "endOffset": 29122, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1580", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/write-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 42741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 42741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 1}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2724, "endOffset": 15185, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2724, "endOffset": 15185, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1581", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/diffOptions.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1271, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1271, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1582", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/read-many-files.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 52914, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 52914, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 3380, "endOffset": 18642, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3380, "endOffset": 18642, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1583", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/memoryTool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26084, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26084, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 418, "count": 1}, {"startOffset": 408, "endOffset": 416, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 470, "endOffset": 525, "count": 1}, {"startOffset": 515, "endOffset": 523, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 575, "endOffset": 628, "count": 1}, {"startOffset": 618, "endOffset": 626, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 685, "endOffset": 745, "count": 2}, {"startOffset": 735, "endOffset": 743, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 799, "endOffset": 856, "count": 1}, {"startOffset": 846, "endOffset": 854, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 897, "endOffset": 941, "count": 1}, {"startOffset": 931, "endOffset": 939, "count": 0}], "isBlockCoverage": true}, {"functionName": "setGeminiMdFilename", "ranges": [{"startOffset": 3915, "endOffset": 4247, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentGeminiMdFilename", "ranges": [{"startOffset": 4249, "endOffset": 4426, "count": 1}, {"startOffset": 4337, "endOffset": 4387, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAllGeminiMdFilenames", "ranges": [{"startOffset": 4428, "endOffset": 4601, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGlobalMemoryFilePath", "ranges": [{"startOffset": 4603, "endOffset": 4740, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureNewlineSeparation", "ranges": [{"startOffset": 4812, "endOffset": 5133, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 5217, "endOffset": 9360, "count": 1}], "isBlockCoverage": true}, {"functionName": "MemoryTool", "ranges": [{"startOffset": 5268, "endOffset": 5392, "count": 0}], "isBlockCoverage": false}, {"functionName": "performAddMemoryEntry", "ranges": [{"startOffset": 5404, "endOffset": 7822, "count": 0}], "isBlockCoverage": false}, {"functionName": "execute", "ranges": [{"startOffset": 7827, "endOffset": 9358, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1584", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/LruCache.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3889, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3889, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 245, "endOffset": 287, "count": 2}, {"startOffset": 277, "endOffset": 285, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 393, "endOffset": 1181, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 423, "endOffset": 515, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 520, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 766, "endOffset": 1131, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1136, "endOffset": 1179, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1585", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/messageInspectors.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2484, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2484, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 352, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFunctionResponse", "ranges": [{"startOffset": 491, "endOffset": 662, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFunctionCall", "ranges": [{"startOffset": 664, "endOffset": 828, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1586", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/shell.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 58783, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 58783, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2048, "endOffset": 20771, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2048, "endOffset": 20771, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1589", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/web-fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37380, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37380, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 249, "endOffset": 295, "count": 1}, {"startOffset": 285, "endOffset": 293, "count": 0}], "isBlockCoverage": true}, {"functionName": "extractUrls", "ranges": [{"startOffset": 1920, "endOffset": 2034, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 2173, "endOffset": 12420, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2173, "endOffset": 12420, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1590", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/generateContentResponseUtilities.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11697, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11697, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 461, "endOffset": 511, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 567, "endOffset": 626, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 679, "endOffset": 735, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 797, "endOffset": 862, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 914, "endOffset": 969, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1030, "endOffset": 1094, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseText", "ranges": [{"startOffset": 1185, "endOffset": 1556, "count": 0}], "isBlockCoverage": false}, {"functionName": "getResponseTextFromParts", "ranges": [{"startOffset": 1558, "endOffset": 1875, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCalls", "ranges": [{"startOffset": 1877, "endOffset": 2226, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromParts", "ranges": [{"startOffset": 2228, "endOffset": 2523, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsAsJson", "ranges": [{"startOffset": 2525, "endOffset": 2734, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFunctionCallsFromPartsAsJson", "ranges": [{"startOffset": 2736, "endOffset": 2957, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponse", "ranges": [{"startOffset": 2959, "endOffset": 3374, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStructuredResponseFromParts", "ranges": [{"startOffset": 3376, "endOffset": 3809, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1591", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/fetch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5599, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5599, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 333, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 425, "endOffset": 475, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1111, "endOffset": 1247, "count": 0}], "isBlockCoverage": true}, {"functionName": "isPrivateIp", "ranges": [{"startOffset": 1249, "endOffset": 1459, "count": 0}], "isBlockCoverage": false}, {"functionName": "fetchWithTimeout", "ranges": [{"startOffset": 1461, "endOffset": 2087, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1624", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/web-search.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 1}, {"startOffset": 287, "endOffset": 295, "count": 0}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1425, "endOffset": 6487, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1425, "endOffset": 6487, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1625", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/client.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 62593, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 62593, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 1}, {"startOffset": 305, "endOffset": 313, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 358, "endOffset": 404, "count": 1}, {"startOffset": 394, "endOffset": 402, "count": 0}], "isBlockCoverage": true}, {"functionName": "isThinkingSupported", "ranges": [{"startOffset": 2827, "endOffset": 2945, "count": 0}], "isBlockCoverage": false}, {"functionName": "findIndexAfterFraction", "ranges": [{"startOffset": 3086, "endOffset": 3730, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3751, "endOffset": 21261, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1626", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/getFolderStructure.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38265, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38265, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "readFullStructure", "ranges": [{"startOffset": 962, "endOffset": 6781, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatStructure", "ranges": [{"startOffset": 7049, "endOffset": 9323, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFolderStructure", "ranges": [{"startOffset": 9744, "endOffset": 12221, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1627", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/turn.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20810, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20810, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 336, "endOffset": 374, "count": 1}, {"startOffset": 364, "endOffset": 372, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1062, "endOffset": 1534, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1658, "endOffset": 6066, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1628", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/errorReporting.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13055, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13055, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 293, "count": 0}], "isBlockCoverage": false}, {"functionName": "reportError", "ranges": [{"startOffset": 1188, "endOffset": 4499, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1629", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/prompts.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 68477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 68477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 360, "endOffset": 414, "count": 1}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "getCoreSystemPrompt", "ranges": [{"startOffset": 2579, "endOffset": 25919, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCompressionPrompt", "ranges": [{"startOffset": 26134, "endOffset": 29221, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1630", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/nextSpeakerChecker.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16737, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16737, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkNextSpeaker", "ranges": [{"startOffset": 3142, "endOffset": 6109, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1631", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/geminiChat.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 69486, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 69486, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidResponse", "ranges": [{"startOffset": 2237, "endOffset": 2536, "count": 0}], "isBlockCoverage": false}, {"functionName": "is<PERSON>alid<PERSON><PERSON>nt", "ranges": [{"startOffset": 2537, "endOffset": 2952, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateHistory", "ranges": [{"startOffset": 3140, "endOffset": 3382, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractCuratedHistory", "ranges": [{"startOffset": 3693, "endOffset": 4789, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4991, "endOffset": 23494, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1632", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/retry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 35868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 35868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultShouldRetry", "ranges": [{"startOffset": 1187, "endOffset": 1726, "count": 0}], "isBlockCoverage": false}, {"functionName": "delay", "ranges": [{"startOffset": 1898, "endOffset": 1982, "count": 0}], "isBlockCoverage": false}, {"functionName": "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2290, "endOffset": 8425, "count": 0}], "isBlockCoverage": false}, {"functionName": "getErrorStatus", "ranges": [{"startOffset": 8585, "endOffset": 9196, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRetryAfterDelayMs", "ranges": [{"startOffset": 9374, "endOffset": 10582, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDelayDurationAndStatus", "ranges": [{"startOffset": 10801, "endOffset": 11060, "count": 0}], "isBlockCoverage": false}, {"functionName": "logRetryAttempt", "ranges": [{"startOffset": 11305, "endOffset": 12428, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1633", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/quotaErrorDetection.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 390, "count": 1}, {"startOffset": 380, "endOffset": 388, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 444, "endOffset": 501, "count": 1}, {"startOffset": 491, "endOffset": 499, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 559, "endOffset": 620, "count": 1}, {"startOffset": 610, "endOffset": 618, "count": 0}], "isBlockCoverage": true}, {"functionName": "isApiError", "ranges": [{"startOffset": 711, "endOffset": 914, "count": 0}], "isBlockCoverage": false}, {"functionName": "isStructuredError", "ranges": [{"startOffset": 916, "endOffset": 1094, "count": 1}], "isBlockCoverage": true}, {"functionName": "isProQuotaExceededError", "ranges": [{"startOffset": 1096, "endOffset": 3182, "count": 0}], "isBlockCoverage": false}, {"functionName": "isGenericQuotaExceededError", "ranges": [{"startOffset": 3184, "endOffset": 3592, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1634", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/loggers.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28116, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28116, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 400, "count": 1}, {"startOffset": 390, "endOffset": 398, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 442, "endOffset": 487, "count": 1}, {"startOffset": 477, "endOffset": 485, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 531, "endOffset": 578, "count": 1}, {"startOffset": 568, "endOffset": 576, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 665, "count": 1}, {"startOffset": 655, "endOffset": 663, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 710, "endOffset": 758, "count": 1}, {"startOffset": 748, "endOffset": 756, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldLogUserPrompts", "ranges": [{"startOffset": 2358, "endOffset": 2408, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCommonAttributes", "ranges": [{"startOffset": 2410, "endOffset": 2515, "count": 0}], "isBlockCoverage": false}, {"functionName": "logCliConfiguration", "ranges": [{"startOffset": 2516, "endOffset": 3703, "count": 0}], "isBlockCoverage": false}, {"functionName": "logUserPrompt", "ranges": [{"startOffset": 3705, "endOffset": 4473, "count": 0}], "isBlockCoverage": false}, {"functionName": "logToolCall", "ranges": [{"startOffset": 4475, "endOffset": 5836, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiRequest", "ranges": [{"startOffset": 5838, "endOffset": 6478, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiError", "ranges": [{"startOffset": 6480, "endOffset": 7878, "count": 0}], "isBlockCoverage": false}, {"functionName": "logApiResponse", "ranges": [{"startOffset": 7880, "endOffset": 9900, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1669", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/sdk.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17109, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17109, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 1}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 371, "endOffset": 424, "count": 1}, {"startOffset": 414, "endOffset": 422, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 472, "endOffset": 523, "count": 1}, {"startOffset": 513, "endOffset": 521, "count": 0}], "isBlockCoverage": true}, {"functionName": "isTelemetrySdkInitialized", "ranges": [{"startOffset": 3850, "endOffset": 3923, "count": 5}], "isBlockCoverage": true}, {"functionName": "parseGrpcEndpoint", "ranges": [{"startOffset": 3925, "endOffset": 4602, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeTelemetry", "ranges": [{"startOffset": 4603, "endOffset": 7042, "count": 0}], "isBlockCoverage": false}, {"functionName": "shutdownTelemetry", "ranges": [{"startOffset": 7044, "endOffset": 7473, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2253", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/clearcut-logger/clearcut-logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49427, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49427, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1936, "endOffset": 17139, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1936, "endOffset": 17139, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2254", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/types.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23860, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23860, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 3}, {"startOffset": 293, "endOffset": 301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 412, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 460, "endOffset": 511, "count": 1}, {"startOffset": 501, "endOffset": 509, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 557, "endOffset": 606, "count": 1}, {"startOffset": 596, "endOffset": 604, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 652, "endOffset": 701, "count": 1}, {"startOffset": 691, "endOffset": 699, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 745, "endOffset": 792, "count": 1}, {"startOffset": 782, "endOffset": 790, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 838, "endOffset": 887, "count": 1}, {"startOffset": 877, "endOffset": 885, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 931, "endOffset": 978, "count": 1}, {"startOffset": 968, "endOffset": 976, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1025, "endOffset": 1075, "count": 1}, {"startOffset": 1065, "endOffset": 1073, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1536, "endOffset": 1696, "count": 1}], "isBlockCoverage": true}, {"functionName": "getDecisionFromOutcome", "ranges": [{"startOffset": 1744, "endOffset": 2414, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2440, "endOffset": 4133, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4157, "endOffset": 4415, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4439, "endOffset": 4813, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4835, "endOffset": 5608, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5632, "endOffset": 5998, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6020, "endOffset": 6562, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 6587, "endOffset": 7724, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2255", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/clearcut-logger/event-metadata-key.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17936, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17936, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 353, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 579, "endOffset": 8381, "count": 1}], "isBlockCoverage": true}, {"functionName": "getEventMetadataKey", "ranges": [{"startOffset": 8429, "endOffset": 8728, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2256", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/user_id.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8899, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8899, "count": 1}, {"startOffset": 1181, "endOffset": 1186, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 354, "endOffset": 406, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureGeminiDirExists", "ranges": [{"startOffset": 1366, "endOffset": 1537, "count": 0}], "isBlockCoverage": false}, {"functionName": "readInstallationIdFromFile", "ranges": [{"startOffset": 1538, "endOffset": 1809, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeInstallationIdToFile", "ranges": [{"startOffset": 1810, "endOffset": 1950, "count": 0}], "isBlockCoverage": false}, {"functionName": "getInstallationId", "ranges": [{"startOffset": 2136, "endOffset": 2597, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGoogleAccountId", "ranges": [{"startOffset": 2902, "endOffset": 3559, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2257", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/uiTelemetry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16712, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16712, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 408, "count": 1}, {"startOffset": 398, "endOffset": 406, "count": 0}], "isBlockCoverage": true}, {"functionName": "createInitialModelMetrics", "ranges": [{"startOffset": 1117, "endOffset": 1361, "count": 0}], "isBlockCoverage": false}, {"functionName": "createInitialMetrics", "ranges": [{"startOffset": 1392, "endOffset": 1786, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1834, "endOffset": 5102, "count": 1}], "isBlockCoverage": true}, {"functionName": "addEvent", "ranges": [{"startOffset": 1910, "endOffset": 2619, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMetrics", "ranges": [{"startOffset": 2624, "endOffset": 2674, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLastPromptTokenCount", "ranges": [{"startOffset": 2679, "endOffset": 2755, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOrCreateModelMetrics", "ranges": [{"startOffset": 2760, "endOffset": 2984, "count": 0}], "isBlockCoverage": false}, {"functionName": "processApiResponse", "ranges": [{"startOffset": 2989, "endOffset": 3653, "count": 0}], "isBlockCoverage": false}, {"functionName": "processApiError", "ranges": [{"startOffset": 3658, "endOffset": 3904, "count": 0}], "isBlockCoverage": false}, {"functionName": "processToolCall", "ranges": [{"startOffset": 3909, "endOffset": 5100, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2258", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/tokenLimits.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 256, "endOffset": 309, "count": 1}, {"startOffset": 299, "endOffset": 307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 394, "count": 1}, {"startOffset": 384, "endOffset": 392, "count": 0}], "isBlockCoverage": true}, {"functionName": "tokenLimit", "ranges": [{"startOffset": 524, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2396", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/services/fileDiscoveryService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 257, "endOffset": 311, "count": 1}, {"startOffset": 301, "endOffset": 309, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 922, "endOffset": 3468, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2398", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/gitIgnoreParser.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8136, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8136, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 938, "endOffset": 2808, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2400", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/services/gitService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14614, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14614, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 247, "endOffset": 291, "count": 1}, {"startOffset": 281, "endOffset": 289, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1582, "endOffset": 5470, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2407", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/memoryDiscovery.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30520, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30520, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 265, "endOffset": 327, "count": 1}, {"startOffset": 317, "endOffset": 325, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 1539, "endOffset": 1603, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1678, "endOffset": 1740, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1816, "endOffset": 1880, "count": 0}], "isBlockCoverage": false}, {"functionName": "findProjectRoot", "ranges": [{"startOffset": 1932, "endOffset": 3025, "count": 0}], "isBlockCoverage": false}, {"functionName": "getGeminiMdFilePathsInternal", "ranges": [{"startOffset": 3026, "endOffset": 6739, "count": 0}], "isBlockCoverage": false}, {"functionName": "readGeminiMdFiles", "ranges": [{"startOffset": 6740, "endOffset": 7792, "count": 0}], "isBlockCoverage": false}, {"functionName": "concatenateInstructions", "ranges": [{"startOffset": 7793, "endOffset": 8432, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadServerHierarchicalMemory", "ranges": [{"startOffset": 8433, "endOffset": 9520, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2408", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/bfsFileSearch.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 250, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 772, "endOffset": 834, "count": 0}], "isBlockCoverage": false}, {"functionName": "bfsFileSearch", "ranges": [{"startOffset": 1124, "endOffset": 2565, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2409", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/memoryImportProcessor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22070, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22070, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 251, "endOffset": 299, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 348, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 830, "endOffset": 894, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 973, "endOffset": 1035, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1115, "endOffset": 1179, "count": 0}], "isBlockCoverage": false}, {"functionName": "processImports", "ranges": [{"startOffset": 1627, "endOffset": 6611, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateImportPath", "ranges": [{"startOffset": 6934, "endOffset": 7386, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2410", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/telemetry/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6384, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6384, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 414, "count": 1}, {"startOffset": 404, "endOffset": 412, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 521, "count": 1}, {"startOffset": 511, "endOffset": 519, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 571, "endOffset": 646, "count": 1}, {"startOffset": 636, "endOffset": 644, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 694, "endOffset": 767, "count": 1}, {"startOffset": 757, "endOffset": 765, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 823, "endOffset": 904, "count": 1}, {"startOffset": 894, "endOffset": 902, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 954, "endOffset": 1029, "count": 1}, {"startOffset": 1019, "endOffset": 1027, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1073, "endOffset": 1142, "count": 1}, {"startOffset": 1132, "endOffset": 1140, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1184, "endOffset": 1251, "count": 1}, {"startOffset": 1241, "endOffset": 1249, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1295, "endOffset": 1364, "count": 1}, {"startOffset": 1354, "endOffset": 1362, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1406, "endOffset": 1473, "count": 1}, {"startOffset": 1463, "endOffset": 1471, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1518, "endOffset": 1588, "count": 1}, {"startOffset": 1578, "endOffset": 1586, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1636, "endOffset": 1709, "count": 1}, {"startOffset": 1699, "endOffset": 1707, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1826, "count": 1}, {"startOffset": 1816, "endOffset": 1824, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1872, "endOffset": 1943, "count": 1}, {"startOffset": 1933, "endOffset": 1941, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1987, "endOffset": 2056, "count": 1}, {"startOffset": 2046, "endOffset": 2054, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2102, "endOffset": 2173, "count": 1}, {"startOffset": 2163, "endOffset": 2171, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2217, "endOffset": 2286, "count": 1}, {"startOffset": 2276, "endOffset": 2284, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2405, "count": 1}, {"startOffset": 2395, "endOffset": 2403, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2450, "endOffset": 2520, "count": 1}, {"startOffset": 2510, "endOffset": 2518, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2560, "endOffset": 2625, "count": 1}, {"startOffset": 2615, "endOffset": 2623, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2674, "endOffset": 2748, "count": 1}, {"startOffset": 2738, "endOffset": 2746, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4190, "endOffset": 4296, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2411", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32748, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32748, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 254, "endOffset": 305, "count": 1}, {"startOffset": 295, "endOffset": 303, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 342, "endOffset": 382, "count": 1}, {"startOffset": 372, "endOffset": 380, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1016, "endOffset": 1088, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1151, "endOffset": 10548, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2412", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/geminiRequest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 258, "endOffset": 313, "count": 1}, {"startOffset": 303, "endOffset": 311, "count": 0}], "isBlockCoverage": true}, {"functionName": "partListUnionToString", "ranges": [{"startOffset": 404, "endOffset": 1583, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2413", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/coreToolScheduler.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 63100, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 63100, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 262, "endOffset": 321, "count": 1}, {"startOffset": 311, "endOffset": 319, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 369, "endOffset": 420, "count": 1}, {"startOffset": 410, "endOffset": 418, "count": 0}], "isBlockCoverage": true}, {"functionName": "createFunctionResponsePart", "ranges": [{"startOffset": 1313, "endOffset": 1523, "count": 0}], "isBlockCoverage": false}, {"functionName": "convertToFunctionResponse", "ranges": [{"startOffset": 1524, "endOffset": 3270, "count": 0}], "isBlockCoverage": false}, {"functionName": "createErrorResponse", "ranges": [{"startOffset": 3300, "endOffset": 3577, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3603, "endOffset": 18668, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2414", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/tools/modifiable-tool.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14519, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14519, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 253, "endOffset": 303, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 350, "endOffset": 400, "count": 0}], "isBlockCoverage": false}, {"functionName": "isModifiableTool", "ranges": [{"startOffset": 1494, "endOffset": 1568, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTempFilesForModify", "ranges": [{"startOffset": 1570, "endOffset": 2615, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUpdatedParams", "ranges": [{"startOffset": 2616, "endOffset": 3645, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteTempFiles", "ranges": [{"startOffset": 3646, "endOffset": 4015, "count": 0}], "isBlockCoverage": false}, {"functionName": "modifyWithEditor", "ranges": [{"startOffset": 4206, "endOffset": 4871, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2415", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/editor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 255, "endOffset": 307, "count": 1}, {"startOffset": 297, "endOffset": 305, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 362, "endOffset": 420, "count": 1}, {"startOffset": 410, "endOffset": 418, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 468, "endOffset": 519, "count": 1}, {"startOffset": 509, "endOffset": 517, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 564, "endOffset": 612, "count": 1}, {"startOffset": 602, "endOffset": 610, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 651, "endOffset": 693, "count": 1}, {"startOffset": 683, "endOffset": 691, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidEditorType", "ranges": [{"startOffset": 1033, "endOffset": 1232, "count": 0}], "isBlockCoverage": false}, {"functionName": "commandExists", "ranges": [{"startOffset": 1233, "endOffset": 1453, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkHasEditorType", "ranges": [{"startOffset": 1836, "endOffset": 2056, "count": 0}], "isBlockCoverage": false}, {"functionName": "allowEditorTypeInSandbox", "ranges": [{"startOffset": 2058, "endOffset": 2289, "count": 0}], "isBlockCoverage": false}, {"functionName": "isEditorAvailable", "ranges": [{"startOffset": 2448, "endOffset": 2637, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDiffCommand", "ranges": [{"startOffset": 2694, "endOffset": 4635, "count": 0}], "isBlockCoverage": false}, {"functionName": "openDiff", "ranges": [{"startOffset": 4847, "endOffset": 6752, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2416", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/core/nonInteractiveToolExecutor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12338, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12338, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 301, "count": 1}, {"startOffset": 291, "endOffset": 299, "count": 0}], "isBlockCoverage": true}, {"functionName": "executeToolCall", "ranges": [{"startOffset": 867, "endOffset": 4170, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "2417", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/core/dist/src/utils/session.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1267, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1267, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 246, "endOffset": 289, "count": 1}, {"startOffset": 279, "endOffset": 287, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "2418", "url": "file:///C:/Users/<USER>/fumbly.ai/packages/cli/src/ui/utils/errorParsing.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18672, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18672, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 259, "endOffset": 315, "count": 1}, {"startOffset": 305, "endOffset": 313, "count": 0}], "isBlockCoverage": true}, {"functionName": "getRateLimitErrorMessageGoogleFree", "ranges": [{"startOffset": 754, "endOffset": 965, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRateLimitErrorMessageGoogleProQuotaFree", "ranges": [{"startOffset": 1018, "endOffset": 1537, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRateLimitErrorMessageGoogleGenericQuotaFree", "ranges": [{"startOffset": 1594, "endOffset": 1893, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRateLimitErrorMessageGooglePaid", "ranges": [{"startOffset": 1938, "endOffset": 2219, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRateLimitErrorMessageGoogleProQuotaPaid", "ranges": [{"startOffset": 2272, "endOffset": 2774, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRateLimitErrorMessageGoogleGenericQuotaPaid", "ranges": [{"startOffset": 2831, "endOffset": 3170, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRateLimitErrorMessageDefault", "ranges": [{"startOffset": 3575, "endOffset": 3786, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRateLimitMessage", "ranges": [{"startOffset": 3788, "endOffset": 5222, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseAndFormatApiError", "ranges": [{"startOffset": 5223, "endOffset": 6704, "count": 1}, {"startOffset": 5451, "endOffset": 5597, "count": 0}, {"startOffset": 5619, "endOffset": 6703, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}]}