{"version": 3, "file": "updateCheck.js", "sourceRoot": "", "sources": ["../../../../src/ui/utils/updateCheck.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,cAAc,MAAM,iBAAiB,CAAC;AAC7C,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AAExD,MAAM,CAAC,KAAK,UAAU,eAAe;IACnC,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,cAAc,EAAE,CAAC;QAC3C,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC9D,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,QAAQ,GAAG,cAAc,CAAC;YAC9B,GAAG,EAAE;gBACH,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,OAAO,EAAE,WAAW,CAAC,OAAO;aAC7B;YACD,mBAAmB;YACnB,mBAAmB,EAAE,CAAC;YACtB,mCAAmC;YACnC,uBAAuB,EAAE,IAAI;SAC9B,CAAC,CAAC;QAEH,IACE,QAAQ,CAAC,MAAM;YACf,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAC1D,CAAC;YACD,OAAO,gCAAgC,QAAQ,CAAC,MAAM,CAAC,OAAO,MAAM,QAAQ,CAAC,MAAM,CAAC,MAAM,wBAAwB,WAAW,CAAC,IAAI,YAAY,CAAC;QACjJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CAAC,+BAA+B,GAAG,CAAC,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC"}